{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' veya sü<PERSON>iz i<PERSON> '-1'.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(örn. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(örn. `sh webui.sh --api`)", "(latest)": "(en son)", "{{ models }}": "{{ models }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}}'ın <PERSON>i", "{{webUIName}} Backend Required": "{{webUIName}} Arka<PERSON><PERSON><PERSON>", "*Prompt node ID(s) are required for image generation": "*Görüntü oluşturma için düğüm kimlikleri gereklidir", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Bir görev modeli, sohbetler ve web arama sorguları için başlık oluşturma gibi görevleri yerine getirirken kullanılır", "a user": "bir kullan<PERSON>ı", "About": "Hakkında", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "<PERSON><PERSON><PERSON>", "Account Activation Pending": "Hesap Aktiva<PERSON>u Bekleniyor", "Actions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "<PERSON><PERSON><PERSON>", "Add": "<PERSON><PERSON>", "Add a model ID": "Bir model <PERSON><PERSON><PERSON><PERSON><PERSON>", "Add a short description about what this model does": "Bu modelin ne yaptığı hakkında kısa bir açıklama ekleyin", "Add a tag": "<PERSON>ir etiket e<PERSON>", "Add Arena Model": "", "Add Connection": "Bağlantı Ekle", "Add Content": "İçerik Ekle", "Add content here": "Buraya içerik ekleyin", "Add custom prompt": "<PERSON><PERSON> prompt e<PERSON><PERSON>", "Add Files": "<PERSON><PERSON><PERSON><PERSON>", "Add Group": "", "Add Memory": "<PERSON><PERSON>", "Add Model": "<PERSON>", "Add Reaction": "", "Add Tag": "Etiket Ekle", "Add Tags": "<PERSON><PERSON><PERSON><PERSON>", "Add text content": "<PERSON><PERSON>", "Add User": "Kullanıcı Ekle", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "<PERSON>u a<PERSON><PERSON><PERSON>, değişiklikleri tüm kullanıcılara evrensel olarak uygulayacaktır.", "admin": "yönetici", "Admin": "Yönetici", "Admin Panel": "Yönetici Paneli", "Admin Settings": "Yönetici Ayarları", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Yönetici<PERSON> her zaman tüm araçlara <PERSON>; kullanıcıların çalışma alanında<PERSON> model ba<PERSON><PERSON><PERSON> atanmış araçlara ihtiyacı vardır.", "Advanced Parameters": "Gelişmiş Parametreler", "Advanced Params": "Gelişmiş Parametreler", "All Documents": "<PERSON><PERSON><PERSON>", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "<PERSON><PERSON><PERSON> Silmeye İzin Ver", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "<PERSON><PERSON> seslere izin verin", "Allow Temporary Chat": "Geçici Sohbetlere İzin Ver", "Allow User Location": "Kullanıcı Konumuna İzin Ver", "Allow Voice Interruption in Call": "Aramada Ses Kesintisine İzin Ver", "Allowed Endpoints": "", "Already have an account?": "Zaten bir hesabın<PERSON>z mı var?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "bir asistan", "and": "ve", "and {{COUNT}} more": "", "and create a new shared link.": "ve yeni bir paylaşılan bağlantı oluşturun.", "API Base URL": "API Temel URL", "API Key": "API Anahtarı", "API Key created.": "API Anahtarı oluşturuldu.", "API Key Endpoint Restrictions": "", "API keys": "API anahtarları", "Application DN": "Uygulama DN", "Application DN Password": "Uygulama DN Parola", "applies to all users with the \"user\" role": "", "April": "<PERSON><PERSON>", "Archive": "Arşiv", "Archive All Chats": "Tüm Sohbetleri Arşivle", "Archived Chats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archived-chat-export": "arşivlenmiş-sohbet-aktarımı", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "Emin misiniz?", "Arena Models": "", "Artifacts": "", "Ask a question": "Bir soru sorun", "Assistant": "Asistan", "Attach file": "<PERSON><PERSON><PERSON>", "Attribute for Username": "Kullanıcı Adı için <PERSON>", "Audio": "Ses", "August": "<PERSON><PERSON><PERSON><PERSON>", "Authenticate": "Kimlik Doğrulama", "Auto-Copy Response to Clipboard": "Yanıtı Panoya Otomatik Kopyala", "Auto-playback response": "Yanıtı otomatik oynatma", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 API Kimlik Doğrulama Dizesi", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 Temel URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 Temel URL gereklidir.", "Available list": "Mevcut liste", "available!": "mevcut!", "Azure AI Speech": "Azure AI Konuşma", "Azure Region": "Azure B<PERSON><PERSON>si", "Back": "<PERSON><PERSON>", "Bad": "", "Bad Response": "Kötü Yanıt", "Banners": "<PERSON><PERSON><PERSON><PERSON>", "Base Model (From)": "Temel Model ('den)", "Batch Size (num_batch)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (num_batch)", "before": "önce", "Beta": "", "BETA": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Brave Search API Anahtarı", "By {{name}}": "", "Bypass SSL verification for Websites": "Web Siteleri için SSL doğrulamasını atlayın", "Call": "<PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "Web STT motoru kullanılırken arama özelliği desteklenmiyor", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "İptal", "Capabilities": "Yetenekler", "Capture": "", "Certificate Path": "", "Change Password": "<PERSON><PERSON><PERSON>", "Channel Name": "", "Channels": "", "Character": "<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "<PERSON><PERSON><PERSON>", "Chat Background Image": "Sohbet Arka Plan Resmi", "Chat Bubble UI": "<PERSON><PERSON><PERSON>lonu <PERSON>ü<PERSON>", "Chat Controls": "<PERSON><PERSON><PERSON>", "Chat direction": "<PERSON><PERSON><PERSON>", "Chat Overview": "Sohbet Genel Bakış", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "<PERSON><PERSON><PERSON><PERSON>", "Check Again": "Tekrar Kontrol <PERSON>", "Check for updates": "Güncellemeleri kontrol et", "Checking for updates...": "Güncellemeler kontrol ediliyor...", "Choose a model before saving...": "Kaydetmeden önce bir model seçin...", "Chunk Overlap": "Chunk Çakışması", "Chunk Params": "Chunk Parametreleri", "Chunk Size": "Chunk Boyutu", "Ciphers": "", "Citation": "Alıntı", "Clear memory": "<PERSON><PERSON><PERSON> te<PERSON>", "click here": "buraya tıklayın", "Click here for filter guides.": "Filtre kılavuzları için buraya tıklayın.", "Click here for help.": "<PERSON><PERSON>m için buraya tıklayın.", "Click here to": "<PERSON><PERSON><PERSON> yapmak için buraya tıklayın:", "Click here to download user import template file.": "Kullanıcı içe aktarma şablon dosyasını indirmek için buraya tıklayın.", "Click here to learn more about faster-whisper and see the available models.": "faster-whisper hakkında daha fazla bilgi edinmek ve mevcut modelleri görmek için buraya tıklayın.", "Click here to select": "Seçmek için buraya tıklayın", "Click here to select a csv file.": "Bir CSV dosyası seçmek için buraya tıklayın.", "Click here to select a py file.": "Bir py dosyası seçmek için buraya tıklayın.", "Click here to upload a workflow.json file.": "Bir workflow.json dosyası yüklemek için buraya tıklayın.", "click here.": "buraya tıklayın.", "Click on the user role button to change a user's role.": "Bir kullanıcının rolünü değiştirmek için kullanıcı rolü düğmesine tıklayın.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Panoya yazma izni reddedildi. Tarayıcı ayarlarını kontrol ederek gerekli izinleri sağlayabilirsiniz.", "Clone": "Klon", "Close": "Ka<PERSON><PERSON>", "Code execution": "", "Code formatted successfully": "Kod başarıyla biçimlendirildi", "Collection": "Koleksiyon", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI Temel URL", "ComfyUI Base URL is required.": "ComfyUI Temel URL gerekli.", "ComfyUI Workflow": "ComfyUI İş Akışı", "ComfyUI Workflow Nodes": "ComfyUI İş Akışı Düğümleri", "Command": "<PERSON><PERSON><PERSON>", "Completions": "", "Concurrent Requests": "Eşzamanlı İstekler", "Configure": "Yapılandırma", "Configure Models": "", "Confirm": "<PERSON><PERSON><PERSON>", "Confirm Password": "Parolayı Onayla", "Confirm your action": "İşleminizi onaylayın", "Confirm your new password": "", "Connections": "Bağlantılar", "Contact Admin for WebUI Access": "WebUI Erişimi için Yöneticiyle İletişime Geçin", "Content": "İçerik", "Content Extraction": "İçerik Çıkarma", "Context Length": "Bağlam Uzunluğu", "Continue Response": "<PERSON><PERSON><PERSON>", "Continue with {{provider}}": "{{provider}} ile devam et", "Continue with Email": "E-posta ile devam edin", "Continue with LDAP": "LDAP ile devam edin", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "<PERSON><PERSON> met<PERSON>in TTS istekleri için nasıl bölüneceğini kontrol edin. '<PERSON><PERSON><PERSON><PERSON>' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 'paragraflar' paragraflara böler ve 'hiçbiri' mesajı tek bir dize olarak tutar.", "Controls": "<PERSON><PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "Çıktının tutarlılığı ve çeşitliliği arasındaki dengeyi kontrol eder. <PERSON><PERSON> dü<PERSON><PERSON><PERSON> bir <PERSON>, <PERSON>ha o<PERSON>mı<PERSON> ve tutarlı bir metinle sonuçlanacaktır. (Varsayılan: 5.0)", "Copied": "Kopyalandı", "Copied shared chat URL to clipboard!": "Paylaşılan sohbet URL'si panoya kopyalandı!", "Copied to clipboard": "Panoya kopyalandı", "Copy": "Kopyala", "Copy last code block": "<PERSON> kod blo<PERSON><PERSON><PERSON> k<PERSON>ala", "Copy last response": "Son yanıtı kopyala", "Copy Link": "Bağlantıyı Kopyala", "Copy to clipboard": "", "Copying to clipboard was successful!": "Panoya kopyalama başarılı!", "Create": "", "Create a knowledge base": "", "Create a model": "Bir model oluştur", "Create Account": "<PERSON><PERSON><PERSON>", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "<PERSON><PERSON>", "Create new secret key": "<PERSON>ni gizli anahtar o<PERSON>", "Created at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarihi", "Created At": "Şu Tarihte Oluşturuldu:", "Created by": "<PERSON><PERSON>un tarafından oluşturuldu:", "CSV Import": "CSV İçe Aktarma", "Current Model": "Mevcut Model", "Current Password": "<PERSON><PERSON><PERSON>", "Custom": "<PERSON><PERSON>", "Dark": "<PERSON><PERSON>", "Database": "Veritabanı", "December": "Aralık", "Default": "Varsayılan", "Default (Open AI)": "", "Default (SentenceTransformers)": "Varsayılan (SentenceTransformers)", "Default Model": "Varsayılan Model", "Default model updated": "Varsayılan model g<PERSON><PERSON><PERSON><PERSON>", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Varsayılan Prompt Önerileri", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "Varsayılan Kullanıcı Rolü", "Delete": "Sil", "Delete a model": "Bir modeli sil", "Delete All Chats": "<PERSON><PERSON><PERSON> Sohbetleri Sil", "Delete All Models": "", "Delete chat": "<PERSON><PERSON><PERSON><PERSON> sil", "Delete Chat": "So<PERSON>bet<PERSON> Sil", "Delete chat?": "<PERSON><PERSON><PERSON>i sil?", "Delete folder?": "", "Delete function?": "Fonksiyonu sil?", "Delete Message": "", "Delete prompt?": "Promptu sil?", "delete this link": "bu bağlantıyı sil", "Delete tool?": "Aracı sil?", "Delete User": "Kullanıcıyı Sil", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} silindi", "Deleted {{name}}": "{{name}} silindi", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "<PERSON><PERSON>ı<PERSON><PERSON>", "Disabled": "Devre Dışı", "Discover a function": "Bir fonksiyon keşfedin", "Discover a model": "Bir model ke<PERSON><PERSON><PERSON>", "Discover a prompt": "Bir prompt ke<PERSON><PERSON><PERSON>", "Discover a tool": "Bir araç k<PERSON><PERSON>din", "Discover wonders": "", "Discover, download, and explore custom functions": "Özel fonksiyonları keşfedin, indirin ve inceleyin", "Discover, download, and explore custom prompts": "<PERSON><PERSON> promptları keş<PERSON>din, indirin ve inceleyin", "Discover, download, and explore custom tools": "<PERSON><PERSON> a<PERSON>ç<PERSON>ı keşfedin, indirin ve inceleyin", "Discover, download, and explore model presets": "Model <PERSON>n ayarların<PERSON> keş<PERSON>din, indirin ve inceleyin", "Dismissible": "Reddedilebilir", "Display": "", "Display Emoji in Call": "<PERSON><PERSON>", "Display the username instead of You in the Chat": "Sohbet'te Siz yerine kullanıcı adını göster", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "Tamamen güvenmediğiniz kaynaklardan fonksiyonlar yüklemeyin.", "Do not install tools from sources you do not fully trust.": "Tamamen güvenmediğiniz kaynaklardan araçlar yüklemeyin.", "Document": "Belge", "Documentation": "Dökümantasyon", "Documents": "<PERSON><PERSON><PERSON>", "does not make any external connections, and your data stays securely on your locally hosted server.": "herhangi bir harici bağlantı yapmaz ve verileriniz güvenli bir şekilde yerel olarak barındırılan sunucunuzda kalır.", "Don't have an account?": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z yok mu?", "don't install random functions from sources you don't trust.": "Tanımadığınız kaynaklardan rastgele fonksiyonlar yüklemeyin.", "don't install random tools from sources you don't trust.": "Tanımadığınız kaynaklardan rastgele araçlar yüklemeyin.", "Done": "Tamamlandı", "Download": "<PERSON><PERSON><PERSON>", "Download canceled": "İndirme iptal edildi", "Download Database": "Veritabanını İndir", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "Sohbete eklemek istediğiniz dosyaları buraya bırakın", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "örn. '30s', '10m'. Geçerli zaman birimleri 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "<PERSON><PERSON><PERSON>", "Edit User": "Kullanıcıyı Düzenle", "Edit User Group": "", "ElevenLabs": "", "Email": "E-posta", "Embark on adventures": "", "Embedding Batch Size": "<PERSON><PERSON><PERSON>ığı<PERSON>", "Embedding Model": "<PERSON><PERSON><PERSON>i", "Embedding Model Engine": "Gömme Modeli Motoru", "Embedding model set to \"{{embedding_model}}\"": "Gömme modeli \"{{embedding_model}}\" olarak <PERSON>landı", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Topluluk Paylaşımını Etkinleştir", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "<PERSON><PERSON>meyi Etkinleştir", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Yeni Kayıtları Etkinleştir", "Enable Web Search": "Web Aramasını Etkinleştir", "Enabled": "<PERSON><PERSON><PERSON>", "Engine": "Motor", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "CSV dosyanızın şu sırayla 4 sütun içerdiğinden emin olun: İsim, E-posta, Şifre, Rol.", "Enter {{role}} message here": "<PERSON><PERSON><PERSON> {{role}} me<PERSON><PERSON><PERSON>n<PERSON> girin", "Enter a detail about yourself for your LLMs to recall": "LLM'lerinizin hatırlaması için kendiniz hakkında bir bilgi girin", "Enter api auth string (e.g. username:password)": "<PERSON>pi auth dizesini girin (örn. kullanıcı adı:parola)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Brave Search API Anahtarını Girin", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "CFG Ölçeğini Girin (örn. 7.0)", "Enter Chunk Overlap": "Chunk Örtüşmesini Girin", "Enter Chunk Size": "Chunk <PERSON>", "Enter description": "", "Enter Github Raw URL": "Github Raw URL'sini girin", "Enter Google PSE API Key": "Google PSE API Anahtarını Girin", "Enter Google PSE Engine Id": "Google PSE Engine Id'sini Girin", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (örn. 512x512)", "Enter Jina API Key": "Jina API Anahtarını Girin", "Enter Kagi Search API Key": "", "Enter language codes": "<PERSON>l kodlarını girin", "Enter Model ID": "Model ID'sini <PERSON>", "Enter model tag (e.g. {{modelTag}})": "Model etiketini girin (örn. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "<PERSON><PERSON><PERSON> G<PERSON>n (örn. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "Örne<PERSON><PERSON><PERSON> (örn. Euler a)", "Enter Scheduler (e.g. Karras)": "Zamanlayıcıyı Girin (örn. <PERSON>)", "Enter Score": "<PERSON><PERSON><PERSON>", "Enter SearchApi API Key": "Arama-API Anahtarını Girin", "Enter SearchApi Engine": "Arama-API Motorunu Girin", "Enter Searxng Query URL": "Searxng Sorgu URL'sini girin", "Enter Seed": "", "Enter Serper API Key": "Serper API Anahtarını Girin", "Enter Serply API Key": "Serply API Anahtarını Girin", "Enter Serpstack API Key": "Serpstack API Anahtarını Girin", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "Durdurma dizisini girin", "Enter system prompt": "Sistem promptunu girin", "Enter Tavily API Key": "Tavily API Anahtarını Girin", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "<PERSON><PERSON> URL'sini G<PERSON>", "Enter Top K": "Top K'yı girin", "Enter URL (e.g. http://127.0.0.1:7860/)": "U<PERSON>'<PERSON><PERSON> (örn. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "U<PERSON>'<PERSON><PERSON> (e.g. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "E-postanızı Girin", "Enter Your Full Name": "Tam Adınızı Girin", "Enter your message": "Mesajınızı girin", "Enter your new password": "", "Enter Your Password": "Parolanızı Girin", "Enter your prompt": "", "Enter Your Role": "Rolünüzü Girin", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "<PERSON><PERSON>", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "<PERSON><PERSON> tut", "Experimental": "Den<PERSON>sel", "Explore the cosmos": "Evreni keşfet", "Export": "Dışa Aktar", "Export All Archived Chats": "", "Export All Chats (All Users)": "Tüm Sohbetleri Dışa Aktar (Tüm Kullanıcılar)", "Export chat (.json)": "<PERSON><PERSON><PERSON><PERSON> dışa aktar (.json)", "Export Chats": "Sohbetleri Dışa Aktar", "Export Config to JSON File": "", "Export Functions": "Fonksiyonları Dışa Aktar", "Export Models": "Modelleri Dışa Aktar", "Export Presets": "", "Export Prompts": "Promptları Dışa Aktar", "Export to CSV": "CSV'ye Aktar", "Export Tools": "Araçları Dışa Aktar", "External Models": "Modelleri Dışa Aktar", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "<PERSON><PERSON><PERSON>.", "Failed to create API Key.": "API Anahtarı oluşturulamadı.", "Failed to read clipboard contents": "Pano içeriği okunamadı", "Failed to save models configuration": "", "Failed to update settings": "<PERSON><PERSON><PERSON>", "February": "Ş<PERSON><PERSON>", "Feedback History": "<PERSON><PERSON> Bildirim G<PERSON>ç<PERSON>şi", "Feedbacks": "<PERSON><PERSON>", "File": "<PERSON><PERSON><PERSON>", "File added successfully.": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> eklendi.", "File content updated successfully.": "Dosya içeriği başarıyla güncellendi.", "File Mode": "<PERSON><PERSON><PERSON>", "File not found.": "<PERSON><PERSON><PERSON> b<PERSON>.", "File removed successfully.": "<PERSON><PERSON>a başar<PERSON>yla kaldırıldı.", "File size should not exceed {{maxSize}} MB.": "<PERSON><PERSON><PERSON> boy<PERSON> {{maxSize}} <PERSON>'<PERSON><PERSON>.", "File uploaded successfully": "", "Files": "<PERSON><PERSON><PERSON><PERSON>", "Filter is now globally disabled": "Filtre artık global olarak devre dışı", "Filter is now globally enabled": "Filtre artık global olarak devrede", "Filters": "<PERSON><PERSON><PERSON><PERSON>", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Parmak izi sahteciliği tespit edildi: <PERSON><PERSON> o<PERSON> baş harfler kullanılamıyor. Varsayılan profil resmine dönülüyor.", "Fluidly stream large external response chunks": "Büyük harici yanıt chunklarını akıcı bir şekilde yayınlayın", "Focus chat input": "<PERSON><PERSON><PERSON>", "Folder deleted successfully": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "Folder name cannot be empty": "Klasör adı boş olamaz", "Folder name cannot be empty.": "", "Folder name updated successfully": "Klasör adı başarıyla güncellendi", "Forge new paths": "<PERSON><PERSON> yollar açın", "Form": "Form", "Format your variables using brackets like this:": "", "Frequency Penalty": "Frekans Cezası", "Function": "Fonksiyon", "Function created successfully": "Fonksiyon başarıyla oluşturuldu", "Function deleted successfully": "Fonksiyon başarı<PERSON> si<PERSON>i", "Function Description": "Fonksiyon Açıklaması", "Function ID": "", "Function is now globally disabled": "Fonksiyon artık global olarak devre dışı", "Function is now globally enabled": "Fonksiyon artık global olarak aktif", "Function Name": "Fonksiyon Adı", "Function updated successfully": "Fonksiyon başarıyla güncellendi", "Functions": "Fonksiyonlar", "Functions allow arbitrary code execution": "Fonksiyonlar keyfi kod yürütülmesine izin verir", "Functions allow arbitrary code execution.": "Fonksiyonlar keyfi kod yürütülmesine izin verir.", "Functions imported successfully": "Fonksiyonlar başarıyla içe aktarıldı", "General": "<PERSON><PERSON>", "General Settings": "<PERSON><PERSON>", "Generate Image": "<PERSON><PERSON><PERSON><PERSON>", "Generating search query": "<PERSON><PERSON> so<PERSON>", "Get started": "Başlayın", "Get started with {{WEBUI_NAME}}": "", "Global": "<PERSON><PERSON><PERSON><PERSON>", "Good Response": "<PERSON>yi <PERSON>", "Google Drive": "", "Google PSE API Key": "Google PSE API Anahtarı", "Google PSE Engine Id": "Google PSE Engine Id", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "Gruplar", "GSA Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "h:mm a": "h:mm a", "Haptic Feedback": "Dokunsal Geri Bildirim", "Harmful or offensive": "", "has no conversations.": "hiç konuşması yok.", "Hello, {{name}}": "<PERSON><PERSON><PERSON><PERSON>, {{name}}", "Help": "Yardım", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "<PERSON><PERSON><PERSON>", "Host": "<PERSON>", "How can I help you today?": "Bugün size nasıl yardımcı olabilirim?", "How would you rate this response?": "", "Hybrid Search": "<PERSON><PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "<PERSON><PERSON><PERSON>min sonuçlarını okuduğumu ve anladığımı kabul ediyorum. Rastgele kod çalıştırmayla ilgili risklerin farkındayım ve kaynağın güvenilirliğini doğruladım.", "ID": "", "Ignite curiosity": "<PERSON><PERSON>", "Image Compression": "", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Deneysel)", "Image Generation Engine": "Görüntü Oluşturma Motoru", "Image Max Compression Size": "", "Image Settings": "Gör<PERSON><PERSON><PERSON>", "Images": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Import Chats": "Sohbetleri İçe Aktar", "Import Config from JSON File": "", "Import Functions": "Fonksiyonları İçe Aktar", "Import Models": "Modelleri İçe Aktar", "Import Presets": "", "Import Prompts": "Promptları İçe Aktar", "Import Tools": "Araçları İçe Aktar", "Include": "<PERSON><PERSON>", "Include `--api-auth` flag when running stable-diffusion-webui": "stable-diffusion-webui <PERSON>ı<PERSON>tırılırken `--api-auth` bayrağını dahil edin", "Include `--api` flag when running stable-diffusion-webui": "stable-diffusion-web<PERSON>ıştırılırken `--api<PERSON> bayrağını dahil edin", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "<PERSON><PERSON><PERSON>", "Input commands": "<PERSON><PERSON><PERSON> komutları", "Install from Github URL": "Github URL'sinden yükley<PERSON>", "Instant Auto-Send After Voice Transcription": "Ses Transkripsiyonundan Sonra Anında Otomatik Gönder", "Interface": "Arayüz", "Invalid file format.": "", "Invalid Tag": "Geçersiz etiket", "is typing...": "", "January": "Ocak", "Jina API Key": "", "join our Discord for help.": "<PERSON>ım için Disco<PERSON>'um<PERSON> katılın.", "JSON": "JSON", "JSON Preview": "JSON Önizlemesi", "July": "Temmuz", "June": "Haziran", "JWT Expiration": "JWT Bitişi", "JWT Token": "JWT Token", "Kagi Search API Key": "", "Keep Alive": "Canlı Tut", "Key": "", "Keyboard shortcuts": "Klavye kısayolları", "Knowledge": "<PERSON><PERSON><PERSON>", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "Dil", "Last Active": "Son Aktivite", "Last Modified": "<PERSON>", "Last reply": "", "Latest users": "", "LDAP": "LDAP", "LDAP server updated": "LDAP sun<PERSON><PERSON>u <PERSON>", "Leaderboard": "Liderlik Tablosu", "Leave empty for unlimited": "Sınırsız için boş bırakınız", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "Varsayılan promptu kullanmak için boş bırakın veya özel bir prompt girin", "Light": "Açık", "Listening...": "Dinleniyor...", "Local": "<PERSON><PERSON>", "Local Models": "<PERSON><PERSON>", "Lost": "<PERSON><PERSON><PERSON>", "LTR": "Soldan Sağa", "Made by OpenWebUI Community": "OpenWebUI Topluluğu tarafından yapılmıştır", "Make sure to enclose them with": "Değişkenlerinizi şu şekilde biçimlendirin:", "Make sure to export a workflow.json file as API format from ComfyUI.": "ComfyUI'dan API formatında bir workflow.json dosyası olarak dışa aktardığınızdan emin olun.", "Manage": "<PERSON><PERSON><PERSON>", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Pipelineları Yönet", "March": "Mart", "Max Tokens (num_predict)": "<PERSON><PERSON><PERSON><PERSON> (num_predict)", "Max Upload Count": "<PERSON><PERSON><PERSON><PERSON> Sayısı", "Max Upload Size": "<PERSON><PERSON><PERSON><PERSON>", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Aynı anda en fazla 3 model indirilebilir. Lütfen daha sonra tekrar deneyin.", "May": "<PERSON><PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "LLM'ler tarafından erişilebilen bellekler burada gösterilecektir.", "Memory": "Bellek", "Memory added successfully": "Bellek başarıyla eklendi", "Memory cleared successfully": "Bellek baş<PERSON><PERSON><PERSON> te<PERSON>", "Memory deleted successfully": "Bellek baş<PERSON><PERSON><PERSON>", "Memory updated successfully": "Bellek başarıyla gü<PERSON>llendi", "Merge Responses": "Yanıtları Birleştir", "Message rating should be enabled to use this feature": "Bu özelliği kullanmak için mesaj derecelendirmesi etkinleştirilmelidir", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Bağlantınızı oluşturduktan sonra gönderdiğiniz mesajlar paylaşılmayacaktır. URL'ye sahip kullanıcılar paylaşılan sohbeti görüntüleyebilecektir.", "Min P": "<PERSON>", "Minimum Score": "Minimum Skor", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "DD MMMM YYYY", "MMMM DD, YYYY HH:mm": "DD MMMM YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "DD MMMM YYYY hh:mm:ss A", "Model": "Model", "Model '{{modelName}}' has been successfully downloaded.": "'{{modelName}}' ba<PERSON><PERSON><PERSON><PERSON> indirildi.", "Model '{{modelTag}}' is already in queue for downloading.": "'{{modelTag}}' zaten indirme sırasında.", "Model {{modelId}} not found": "{{modelId}} bulunamadı", "Model {{modelName}} is not vision capable": "Model {{modelName}} g<PERSON><PERSON><PERSON><PERSON><PERSON>", "Model {{name}} is now {{status}}": "{{name}} modeli art<PERSON> {{status}}", "Model accepts image inputs": "Model g<PERSON><PERSON><PERSON><PERSON><PERSON> girdilerini kabul eder", "Model created successfully!": "Model ba<PERSON><PERSON><PERSON><PERSON> oluşturuldu!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Model <PERSON><PERSON> siste<PERSON> yolu al<PERSON>. Güncelleme için model k<PERSON><PERSON> ad<PERSON>, devam edilemiyor.", "Model Filtering": "", "Model ID": "Model ID", "Model IDs": "<PERSON> <PERSON><PERSON><PERSON>", "Model Name": "Model Adı", "Model not selected": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "Model Params": "Model Parametreleri", "Model Permissions": "", "Model updated successfully": "Model b<PERSON><PERSON><PERSON><PERSON><PERSON>", "Modelfile Content": "Model Dosyası İçeriği", "Models": "Modeller", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "<PERSON><PERSON>", "Name": "Ad", "Name your knowledge base": "", "New Chat": "<PERSON><PERSON>", "New folder": "", "New Password": "<PERSON><PERSON>", "new-channel": "", "No content found": "İçerik bulunamadı", "No content to speak": "Konuşacak içerik yok", "No distance available": "<PERSON><PERSON> mevcut de<PERSON>", "No feedbacks found": "<PERSON><PERSON> bi<PERSON><PERSON><PERSON> bulu<PERSON>ı", "No file selected": "Hiçbir dosya seçilmedi", "No files found.": "<PERSON><PERSON><PERSON> b<PERSON>.", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "HTML, CSS veya JavaScript içeriği bulunamadı.", "No knowledge found": "<PERSON><PERSON><PERSON> bulunamadı", "No model IDs": "", "No models found": "Model bulunamadı", "No models selected": "", "No results found": "<PERSON><PERSON><PERSON> bulu<PERSON>adı", "No search query generated": "Hiç arama sorgusu oluşturulmadı", "No source available": "<PERSON><PERSON>k mevcut de<PERSON>", "No users were found.": "", "No valves to update": "Güncellenecek valvler yok", "None": "Yok", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Not: Minimum bir skor belirlerseniz, arama yalnızca minimum skora eşit veya daha yüksek bir skora sahip belgeleri getirecektir.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "November": "Kasım", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "OAuth ID", "October": "<PERSON><PERSON>", "Off": "<PERSON><PERSON><PERSON>", "Okay, Let's Go!": "<PERSON><PERSON>, <PERSON><PERSON> Başlayalım!", "OLED Dark": "OLED Koyu", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "Ollama API'si devre dışı", "Ollama API settings updated": "", "Ollama Version": "Ollama Sürümü", "On": "Açık", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Komut dizisinde yalnızca alfasayısal karakterler ve tireler kabul edilir.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Hop! URL geçersiz gibi görünüyor. Lütfen tekrar kontrol edin ve yeniden deneyin.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Hop! Desteklenmeyen bir yöntem kullanıyorsunuz (yalnızca önyüz). Lütfen WebUI'yi arkayüzden sunun.", "Open in full screen": "", "Open new chat": "<PERSON><PERSON> so<PERSON> aç", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open-WebUI sürümü (v{{OPEN_WEBUI_VERSION}}) gere<PERSON><PERSON> sürümden (v{{REQUIRED_VERSION}}) düşük", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API Konfigürasyonu", "OpenAI API Key is required.": "OpenAI API Anahtarı gereklidir.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "OpenAI URL/Anahtar gereklidir.", "or": "veya", "Organize your users": "", "OUTPUT": "", "Output format": "Çıkt<PERSON> formatı", "Overview": "Genel Bakış", "page": "", "Password": "Pa<PERSON><PERSON>", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF belgesi (.pdf)", "PDF Extract Images (OCR)": "PDF Görüntülerini Çıkart (OCR)", "pending": "be<PERSON><PERSON>e", "Permission denied when accessing media devices": "Medya c<PERSON>azlarına erişim izni reddedildi", "Permission denied when accessing microphone": "Mikrofona erişim izni reddedildi", "Permission denied when accessing microphone: {{error}}": "Mikrofona erişim izni reddedildi: {{error}}", "Permissions": "", "Personalization": "<PERSON><PERSON><PERSON><PERSON>", "Pin": "<PERSON><PERSON><PERSON>", "Pinned": "Sabitlenmiş", "Pioneer insights": "", "Pipeline deleted successfully": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>i", "Pipeline downloaded successfully": "Pipeline başarıyla g<PERSON>", "Pipelines": "Pipelinelar", "Pipelines Not Detected": "Pipeline Tespit Edilmedi", "Pipelines Valves": "<PERSON><PERSON><PERSON>", "Plain text (.txt)": "<PERSON><PERSON><PERSON> metin (.txt)", "Playground": "<PERSON><PERSON> Alanı", "Please carefully review the following warnings:": "Lütfen aşağıdaki uyarıları dikkatlice inceleyin:", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "Önceki 30 gün", "Previous 7 days": "Önceki 7 gün", "Profile Image": "Profil Fotoğrafı", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (örn. Roma İmparatorluğu hakkında ilginç bir bilgi verin)", "Prompt Content": "Prompt İçeriği", "Prompt created successfully": "", "Prompt suggestions": "Prompt önerileri", "Prompt updated successfully": "", "Prompts": "Promptlar", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "Ollama.com'dan \"{{searchValue}}\" çekin", "Pull a model from Ollama.com": "Ollama.com'dan bir model <PERSON><PERSON><PERSON>", "Query Generation Prompt": "", "Query Params": "<PERSON><PERSON><PERSON>", "RAG Template": "RAG Şablonu", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "<PERSON><PERSON><PERSON>", "Record voice": "<PERSON><PERSON> kaydı yap", "Redirecting you to OpenWebUI Community": "OpenWebUI Topluluğuna yönlendiriliyorsunuz", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Kendinizden \"User\" o<PERSON><PERSON> b<PERSON> (<PERSON><PERSON><PERSON><PERSON>, \"User İspanyolca öğreniyor\")", "References from": "", "Refresh Token Expiration": "", "Regenerate": "<PERSON><PERSON><PERSON>", "Release Notes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Relevance": "", "Remove": "Kaldır", "Remove Model": "<PERSON><PERSON>", "Rename": "<PERSON><PERSON><PERSON>", "Reorder Models": "", "Repeat Last N": "<PERSON><PERSON>", "Reply in Thread": "", "Request Mode": "İstek Modu", "Reranking Model": "<PERSON><PERSON><PERSON> Modeli", "Reranking model disabled": "Yeniden sıralama modeli devre dışı bırakıldı", "Reranking model set to \"{{reranking_model}}\"": "<PERSON><PERSON><PERSON> sı<PERSON>ama modeli \"{{reranking_model}}\" olar<PERSON> a<PERSON>landı", "Reset": "Sıfırla", "Reset All Models": "", "Reset Upload Directory": "<PERSON><PERSON><PERSON><PERSON> Dizinini <PERSON>", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Web sitesi izinleri reddedildiğinden yanıt bildirimleri etkinleştirilemiyor. Gerekli erişimi sağlamak için lütfen tarayıcı ayarlarınızı ziyaret edin.", "Response splitting": "<PERSON><PERSON><PERSON>", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "Rol", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "<PERSON><PERSON><PERSON>", "Run": "Çalıştır", "Running": "Çalışıyor", "Save": "<PERSON><PERSON>", "Save & Create": "<PERSON><PERSON> ve <PERSON>luştur", "Save & Update": "<PERSON><PERSON>", "Save As Copy": "<PERSON><PERSON><PERSON>", "Save Tag": "<PERSON><PERSON><PERSON><PERSON>", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Sohbet kayıtlarının doğrudan tarayıcınızın depolama alanına kaydedilmesi artık desteklenmemektedir. Lütfen aşağıdaki butona tıklayarak sohbet kayıtlarınızı indirmek ve silmek için bir dakikanızı ayırın. Endişelenmeyin, sohbet günlüklerinizi arkayüze kolayca yeniden aktarabilirsiniz:", "Scroll to bottom when switching between branches": "<PERSON><PERSON> arasında geçiş yaparken en alta kaydır", "Search": "Ara", "Search a model": "Bir model ara", "Search Base": "", "Search Chats": "Sohbetleri Ara", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "Fonksiyonları Ara", "Search Knowledge": "", "Search Models": "Modelleri Ara", "Search options": "", "Search Prompts": "Prompt Ara", "Search Result Count": "Arama Sonuc<PERSON>", "Search Tools": "Arama <PERSON>", "Search users": "", "SearchApi API Key": "Arama-API API Anahtarı", "SearchApi Engine": "Arama-API Motoru", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "\"{{searchQuery}}\" aranıyor", "Searching Knowledge for \"{{searchQuery}}\"": "\"{{searchQuery}}\" i<PERSON><PERSON>", "Searxng Query URL": "Searxng Sorgu URL'si", "See readme.md for instructions": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>in readme.md dosyasına bakın", "See what's new": "Yeniliklere göz atın", "Seed": "Seed", "Select a base model": "Bir temel model seç", "Select a engine": "Bir motor seç", "Select a function": "Bir fonksiyon seç", "Select a group": "", "Select a model": "Bir model seç", "Select a pipeline": "Bir pipeline seç", "Select a pipeline url": "Bir pipeline URL'si seç", "Select a tool": "Bir araç seç", "Select Engine": "Motor Seç", "Select Knowledge": "", "Select model": "Model seç", "Select only one model to call": "<PERSON><PERSON><PERSON> sad<PERSON>e bir model seç", "Selected model(s) do not support image inputs": "Seçilen model(ler) görüntü girişlerini desteklemiyor", "Semantic distance to query": "", "Send": "<PERSON><PERSON><PERSON>", "Send a message": "", "Send a Message": "<PERSON><PERSON> <PERSON><PERSON>", "Send message": "<PERSON><PERSON>", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "İsteğe `stream_options: { include_usage: true }` gönderir.\nDesteklenen sağlayıcılar, ayarlandığında yanıtta token kullanım bilgilerini döndürecektir.", "September": "<PERSON><PERSON><PERSON><PERSON>", "Serper API Key": "Serper API Anahtarı", "Serply API Key": "Serply API Anahtarı", "Serpstack API Key": "Serpstack API Anahtarı", "Server connection verified": "<PERSON><PERSON><PERSON> bağlantısı doğrulandı", "Set as default": "Varsayılan olarak a<PERSON>", "Set CFG Scale": "CFG Ölçeğini Ayarla", "Set Default Model": "Varsayılan Modeli Ayarla", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Gömme modelini ayarlayın (örn. {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "Ye<PERSON>den sıralama modelini ayarlayın (örn. {{model}})", "Set Sampler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Set Scheduler": "Zamanlayıcıyı Ayarla", "Set Steps": "Adımları Ayarla", "Set Task Model": "G<PERSON>rev <PERSON>la", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "<PERSON><PERSON>", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "<PERSON><PERSON><PERSON>", "Settings saved successfully!": "<PERSON><PERSON><PERSON> başar<PERSON>yla kaydedildi!", "Share": "Paylaş", "Share Chat": "<PERSON><PERSON><PERSON><PERSON>", "Share to OpenWebUI Community": "OpenWebUI Topluluğu ile Paylaş", "Show": "<PERSON><PERSON><PERSON>", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "Yönetici Ayrıntılarını Hesap Bekliyor Ekranında Göster", "Show shortcuts": "Kısayolları göster", "Show your support!": "Desteğinizi gösterin!", "Sign in": "Oturum aç", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "Çıkış Yap", "Sign up": "<PERSON><PERSON><PERSON>", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "<PERSON><PERSON><PERSON>", "Speech Playback Speed": "Konuşma Oynatma Hızı", "Speech recognition error: {{error}}": "Konuşma tanıma hatası: {{error}}", "Speech-to-Text Engine": "Konuşmadan Metne Motoru", "Stop": "", "Stop Sequence": "<PERSON><PERSON><PERSON>", "Stream Chat Response": "", "STT Model": "STT Modeli", "STT Settings": "STT Ayarları", "Success": "Başarılı", "Successfully updated.": "Başarıyla güncellendi.", "Suggested prompts to get you started": "", "Support": "Destek", "Support this plugin:": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> deste<PERSON>:", "Sync directory": "", "System": "Sistem", "System Instructions": "", "System Prompt": "Sistem Promptu", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "Durdurmak iç<PERSON> do<PERSON>n", "Tavily API Key": "Tavily API Anahtarı", "Temperature": "Temperature", "Template": "Şablon", "Temporary Chat": "Geçici Sohbet", "Text Splitter": "", "Text-to-Speech Engine": "Metinden Sese Motoru", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "<PERSON><PERSON> bildiri<PERSON>z i<PERSON> teşekkürler!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Bu eklentinin arkasındaki geliştiriciler topluluktan tutkulu gönüllülerdir. Bu eklentinin yararlı olduğunu dü<PERSON>ü<PERSON>ü<PERSON>n<PERSON>z, gel<PERSON><PERSON><PERSON><PERSON> katkıda bulunmayı düşünün.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "MB cinsinden maksimum dosya boyutu. <PERSON><PERSON>a boyutu bu sınır<PERSON> aşarsa, dosya yüklenmeyecektir.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Sohbette aynı anda kullanılabilecek maksimum dosya sayısı. Dosya sayısı bu sınırı aşarsa, dosyalar yüklenmeyecektir.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Puan 0.0 (%0) ile 1.0 (%100) arasında bir değer olmalıdır.", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "<PERSON><PERSON>", "Thinking...": "Düşünüyor...", "This action cannot be undone. Do you wish to continue?": "<PERSON>u eylem geri alınamaz. Devam etmek istiyor musunuz?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Bu, önemli konuşmalarınızın güvenli bir şekilde arkayüz veritabanınıza kaydedildiğini garantiler. Teşekkür ederiz!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "<PERSON>u deneysel bir <PERSON>, beklendiği gibi çalışmayabilir ve her an değişiklik yapılabilir.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "<PERSON><PERSON>", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "<PERSON><PERSON> Sunucu URL'si gereklidir.", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "İpucu: Her değiştirmeden sonra sohbet girişinde tab tuşuna basarak birden fazla değ<PERSON>şken yuvasını art arda günce<PERSON>yin.", "Title": "Başlık", "Title (e.g. Tell me a fun fact)": "Başlık (e.g. Ban<PERSON> ilginç bir bilgi ver)", "Title Auto-Generation": "Otomatik Başlık Oluşturma", "Title cannot be an empty string.": "Başlık boş bir dize olamaz.", "Title Generation Prompt": "Başlık Oluşturma Promptu", "TLS": "", "To access the available model names for downloading,": "İndirilebilir mevcut model <PERSON><PERSON><PERSON><PERSON>,", "To access the GGUF models available for downloading,": "İndirilebilir mevcut GGUF modellerine erişmek için,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "WebUI'ye erişmek için lütfen yöneticiyle iletişime geçin. Yöneticiler kullanıcı durumlarını Yönetici Panelinden yönetebilir.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "Burada eylemleri seçmek için öncelikle bunları \"İşlevler\" çalışma alanına ekleyin.", "To select filters here, add them to the \"Functions\" workspace first.": "Filtreleri burada seçmek için öncelikle bunları \"İşlevler\" çalışma alanına ekleyin.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Araçları burada seçmek için öncelikle bunları \"Araçlar\" çalışma alanına ekleyin.", "Toast notifications for new updates": "", "Today": "<PERSON><PERSON><PERSON><PERSON>", "Toggle settings": "Ayarları Aç/Kapat", "Toggle sidebar": "Kenar <PERSON>u Aç/Kapat", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "Bağlam Yenilemesinde Korunacak Tokenler (num_keep)", "Tool created successfully": "<PERSON>ç başarıyla oluşturuldu", "Tool deleted successfully": "<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "Araç başarıyla içe aktarıldı", "Tool Name": "", "Tool updated successfully": "<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "Tools": "Araçlar", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "<PERSON><PERSON><PERSON>, keyfi kod yürütme ile bir fonksiyon çağırma sistemine sa<PERSON>r", "Tools have a function calling system that allows arbitrary code execution": "<PERSON><PERSON><PERSON>, keyfi kod yürütme izni veren bir fonksiyon çağırma sistemine sa<PERSON>tir", "Tools have a function calling system that allows arbitrary code execution.": "<PERSON><PERSON><PERSON>, keyfi kod yürütme izni veren bir fonksiyon çağırma sistemine sa<PERSON>tir.", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "<PERSON><PERSON><PERSON><PERSON>ya erişmede sorun mu yaşıyorsunuz?", "TTS Model": "TTS Modeli", "TTS Settings": "TTS Ayarları", "TTS Voice": "TTS Sesi", "Type": "<PERSON><PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "HuggingFace Çözümleme (İndirme) URL'sini Yazın", "Uh-oh! There was an issue with the response.": "", "UI": "Arayüz", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "Sabitlemeyi Kaldır", "Unravel secrets": "", "Untagged": "", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update and Copy Link": "Güncelle ve Bağlantıyı Kopyala", "Update for the latest features and improvements.": "", "Update password": "Parolayı Güncelle", "Updated": "", "Updated at": "<PERSON><PERSON> ta<PERSON> gü<PERSON>:", "Updated At": "", "Upload": "<PERSON><PERSON><PERSON>", "Upload a GGUF model": "Bir GGUF modeli yükle", "Upload directory": "", "Upload files": "", "Upload Files": "Dosyaları Yükle", "Upload Pipeline": "<PERSON><PERSON><PERSON>", "Upload Progress": "<PERSON><PERSON><PERSON><PERSON>", "URL": "", "URL Mode": "URL Modu", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "<PERSON><PERSON><PERSON><PERSON>", "Use groups to group your users and assign permissions.": "", "Use Initials": "Baş Harfleri Kullan", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "kullanıcı", "User": "", "User location successfully retrieved.": "Kullanıcı konumu başarıyla alındı.", "Username": "", "Users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "<PERSON><PERSON>", "Valid time units:": "Geçerli zaman birimleri:", "Valves": "<PERSON><PERSON><PERSON>", "Valves updated": "<PERSON><PERSON><PERSON>", "Valves updated successfully": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "variable": "değişken", "variable to have them replaced with clipboard content.": "panodaki içerikle değiştirilmesi için <PERSON>.", "Version": "S<PERSON>r<PERSON><PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "Ses", "Voice Input": "", "Warning": "Uyarı", "Warning:": "Uyarı:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Uyarı: Gömme modelinizi günceller veya <PERSON>ğiştirirseniz, tüm belgeleri yeniden içe aktarmanız gerekecektir.", "Web": "Web", "Web API": "Web API", "Web Loader Settings": "Web Yükleyici Ayarları", "Web Search": "Web Araması", "Web Search Engine": "Web Arama Motoru", "Web Search Query Generation": "", "Webhook URL": "Webhook URL", "WebUI Settings": "WebUI Ayarları", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "<PERSON><PERSON><PERSON><PERSON>:", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "Whisper (<PERSON><PERSON>)", "Widescreen Mode": "Geniş Ekran <PERSON>", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "Çalışma Alanı", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "Bir prompt <PERSON><PERSON><PERSON> ya<PERSON>ın (örn. <PERSON> kimsin?)", "Write a summary in 50 words that summarizes [topic or keyword].": "[Konuyu veya anahtar kelimeyi] özetleyen 50 kelimelik bir özet yazın.", "Write something...": "", "Write your model template content here": "", "Yesterday": "<PERSON><PERSON><PERSON>", "You": "<PERSON>", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "<PERSON><PERSON>ı anda en fazla {{maxCount}} dosya ile sohbet edebilirsiniz.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Aşağıdaki 'Yönet' düğmesi aracılığıyla bellekler ekleyerek LLM'lerle etkileşimlerinizi kişiselleştirebilir, onları daha yararlı ve size özel hale getirebilirsiniz.", "You cannot upload an empty file.": "", "You have no archived conversations.": "Arşivlenmiş sohbetleriniz yok.", "You have shared this chat": "<PERSON>u sohbeti paylaştınız", "You're a helpful assistant.": "<PERSON>ımsever bir asistansın.", "Your account status is currently pending activation.": "<PERSON><PERSON><PERSON> anda et<PERSON>ştirilmeyi bekliyor.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Tüm katkınız doğrudan eklenti geliştiricisine gidecektir; Open WebUI herhangi bir yüzde almaz. Ancak seçilen finansman platformunun kendi ücretleri olabilir.", "Youtube": "Youtube", "Youtube Loader Settings": "Youtube Yükleyici Ayarları"}