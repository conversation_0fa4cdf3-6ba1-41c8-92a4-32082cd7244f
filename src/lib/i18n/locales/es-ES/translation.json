{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' o '-1' para evitar expiración.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(p.ej. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(p.ej. `sh webui.sh --api`)", "(latest)": "(latest)", "{{ models }}": "{{ models }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "Chats de {{user}}", "{{webUIName}} Backend Required": "{{webUIName}} Ser<PERSON>or <PERSON>", "*Prompt node ID(s) are required for image generation": "Los ID de nodo son requeridos para la generación de imágenes", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Un modelo de tareas se utiliza cuando se realizan tareas como la generación de títulos para chats y consultas de búsqueda web", "a user": "un usuario", "About": "Sobre nosotros", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "C<PERSON><PERSON>", "Account Activation Pending": "Activación de cuenta pendiente", "Actions": "Acciones", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "Usuarios activos", "Add": "Agregar", "Add a model ID": "", "Add a short description about what this model does": "Agregue una breve descripción sobre lo que hace este modelo", "Add a tag": "Agregar una etiqueta", "Add Arena Model": "", "Add Connection": "", "Add Content": "Agregar <PERSON>", "Add content here": "Agrege contenido aquí", "Add custom prompt": "Agregar un prompt personalizado", "Add Files": "Agregar Archivos", "Add Group": "", "Add Memory": "Agregar <PERSON>", "Add Model": "Agregar <PERSON>", "Add Reaction": "", "Add Tag": "Agregar etiqueta", "Add Tags": "agregar etiquetas", "Add text content": "Añade contenido de texto", "Add User": "<PERSON><PERSON><PERSON><PERSON>", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Ajustar estas opciones aplicará los cambios universalmente a todos los usuarios.", "admin": "admin", "Admin": "Admin", "Admin Panel": "Panel de Administración", "Admin Settings": "Configuración de Administrador", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Admins tienen acceso a todas las herramientas en todo momento; los usuarios necesitan herramientas asignadas por modelo en el espacio de trabajo.", "Advanced Parameters": "<PERSON>rá<PERSON><PERSON>", "Advanced Params": "Pará<PERSON><PERSON>", "All Documents": "Todos los Documentos", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "<PERSON><PERSON><PERSON>", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "Permitir voces no locales", "Allow Temporary Chat": "<PERSON><PERSON><PERSON>", "Allow User Location": "Permitir Ubicación del Usuario", "Allow Voice Interruption in Call": "Permitir interrupción de voz en llamada", "Allowed Endpoints": "", "Already have an account?": "¿Ya tienes una cuenta?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "un asistente", "and": "y", "and {{COUNT}} more": "", "and create a new shared link.": "y crear un nuevo enlace compartido.", "API Base URL": "Dirección URL de la API", "API Key": "Clave de la API ", "API Key created.": "Clave de la API creada.", "API Key Endpoint Restrictions": "", "API keys": "Claves de la API", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "Abril", "Archive": "Archivar", "Archive All Chats": "Archivar todos los chats", "Archived Chats": "Chats archivados", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "¿E<PERSON>á seguro?", "Arena Models": "", "Artifacts": "Artefactos", "Ask a question": "Haz una pregunta", "Assistant": "", "Attach file": "Adjuntar archivo", "Attribute for Username": "", "Audio": "Audio", "August": "Agosto", "Authenticate": "", "Auto-Copy Response to Clipboard": "Copiar respuesta automáticamente al portapapeles", "Auto-playback response": "Respuesta de reproducción automática", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "Cadena de autenticación de API", "AUTOMATIC1111 Base URL": "Dirección URL de AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "La dirección URL de AUTOMATIC1111 es requerida.", "Available list": "Lista disponible", "available!": "¡disponible!", "Azure AI Speech": "", "Azure Region": "Región de Azure", "Back": "Volver", "Bad": "", "Bad Response": "Respuesta <PERSON>a", "Banners": "Banners", "Base Model (From)": "Modelo base (desde)", "Batch Size (num_batch)": "<PERSON><PERSON><PERSON> (num_batch)", "before": "antes", "Beta": "", "BETA": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Clave de API de Brave Search", "By {{name}}": "", "Bypass SSL verification for Websites": "Desactivar la verificación SSL para sitios web", "Call": "Llamada", "Call feature is not supported when using Web STT engine": "La funcionalidad de llamada no puede usarse junto con el motor de STT Web", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON>", "Capabilities": "Capacidades", "Capture": "", "Certificate Path": "", "Change Password": "Cambia la Contraseña", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "Cha<PERSON>", "Chat Background Image": "Imágen de fondo del Chat", "Chat Bubble UI": "Burbuja de chat UI", "Chat Controls": "Controles de chat", "Chat direction": "Dirección del Chat", "Chat Overview": "Vista general del chat", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "Chats", "Check Again": "Verifica de nuevo", "Check for updates": "Verificar actualizaciones", "Checking for updates...": "Verificando actualizaciones...", "Choose a model before saving...": "Escoge un modelo antes de guardar los cambios...", "Chunk Overlap": "Superposición de fragmentos", "Chunk Params": "Parámetros de fragmentos", "Chunk Size": "Tamaño de fragmentos", "Ciphers": "", "Citation": "Cita", "Clear memory": "Liberar memoria", "click here": "", "Click here for filter guides.": "", "Click here for help.": "Presiona aquí para obtener ayuda.", "Click here to": "Presiona aquí para", "Click here to download user import template file.": "Presiona aquí para descargar el archivo de plantilla de importación de usuario.", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "Presiona aquí para seleccionar", "Click here to select a csv file.": "Presiona aquí para seleccionar un archivo csv.", "Click here to select a py file.": "Presiona aquí para seleccionar un archivo py.", "Click here to upload a workflow.json file.": "Presiona aquí para subir un archivo workflow.json.", "click here.": "Presiona aquí.", "Click on the user role button to change a user's role.": "Presiona en el botón de roles del usuario para cambiar su rol.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Permisos de escritura del portapapeles denegados. Por favor, comprueba las configuraciones de tu navegador para otorgar el acceso necesario.", "Clone": "Clonar", "Close": "<PERSON><PERSON><PERSON>", "Code execution": "", "Code formatted successfully": "Se ha formateado correctamente el código.", "Collection": "Colección", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI Base URL", "ComfyUI Base URL is required.": "ComfyUI Base URL es requerido.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "Nodos para ComfyUI Workflow", "Command": "Comand<PERSON>", "Completions": "", "Concurrent Requests": "Solicitudes simultáneas", "Configure": "", "Configure Models": "", "Confirm": "Confirmar", "Confirm Password": "Con<PERSON><PERSON><PERSON>", "Confirm your action": "Confirma tu acción", "Confirm your new password": "", "Connections": "Conexiones", "Contact Admin for WebUI Access": "Contacta el administrador para obtener acceso al WebUI", "Content": "Contenido", "Content Extraction": "Extracción de contenido", "Context Length": "Longitud del contexto", "Continue Response": "<PERSON><PERSON><PERSON><PERSON>", "Continue with {{provider}}": "Continuar con {{provider}}", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "Controles", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "Copiado", "Copied shared chat URL to clipboard!": "¡URL de chat compartido copiado al portapapeles!", "Copied to clipboard": "Copiado al portapapeles", "Copy": "Copiar", "Copy last code block": "Copia el último bloque de código", "Copy last response": "Copia la última respuesta", "Copy Link": "<PERSON><PERSON><PERSON> enlace", "Copy to clipboard": "", "Copying to clipboard was successful!": "¡La copia al portapapeles se ha realizado correctamente!", "Create": "", "Create a knowledge base": "", "Create a model": "<PERSON><PERSON>r un modelo", "Create Account": "<PERSON><PERSON><PERSON> una cuenta", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "<PERSON><PERSON><PERSON>", "Create new key": "<PERSON><PERSON>r una nueva clave", "Create new secret key": "<PERSON><PERSON>r una nueva clave secreta", "Created at": "Creado en", "Created At": "Creado en", "Created by": "<PERSON><PERSON>o por", "CSV Import": "Importa un CSV", "Current Model": "Modelo Actual", "Current Password": "Contraseña Actual", "Custom": "Personalizado", "Dark": "Oscuro", "Database": "Base de datos", "December": "Diciembre", "Default": "Por defecto", "Default (Open AI)": "Predetermina<PERSON> (Open AI)", "Default (SentenceTransformers)": "Predeterminado (SentenceTransformers)", "Default Model": "<PERSON><PERSON> predeterminado", "Default model updated": "El modelo por defecto ha sido actualizado", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Sugerencias de mensajes por defecto", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "Rol por defecto para usuarios", "Delete": "Bo<PERSON>r", "Delete a model": "Borra un modelo", "Delete All Chats": "Eliminar todos los chats", "Delete All Models": "", "Delete chat": "Bo<PERSON>r chat", "Delete Chat": "<PERSON><PERSON><PERSON>", "Delete chat?": "Bo<PERSON>r el chat?", "Delete folder?": "", "Delete function?": "Borrar la función?", "Delete Message": "", "Delete prompt?": "<PERSON><PERSON>r el prompt?", "delete this link": "Bo<PERSON>r este enlace", "Delete tool?": "Borrar la herramienta", "Delete User": "<PERSON><PERSON><PERSON>", "Deleted {{deleteModelTag}}": "Se borró {{deleteModelTag}}", "Deleted {{name}}": "Eliminado {{nombre}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "Descripción", "Disabled": "Desactivado", "Discover a function": "Descubre una función", "Discover a model": "Descubrir un modelo", "Discover a prompt": "Descubre un Prompt", "Discover a tool": "Descubre una herramienta", "Discover wonders": "", "Discover, download, and explore custom functions": "Descubre, descarga y explora funciones personalizadas", "Discover, download, and explore custom prompts": "Des<PERSON><PERSON>, descarga, y explora Prompts personalizados", "Discover, download, and explore custom tools": "Descubre, descarga y explora herramientas personalizadas", "Discover, download, and explore model presets": "Descubre, descarga y explora ajustes preestablecidos de modelos", "Dismissible": "Desestimable", "Display": "", "Display Emoji in Call": "Muestra Emoji en llamada", "Display the username instead of You in the Chat": "Mostrar el nombre de usuario en lugar de Usted en el chat", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "No instale funciones desde fuentes que no confíe totalmente.", "Do not install tools from sources you do not fully trust.": "No instale herramientas desde fuentes que no confíe totalmente.", "Document": "Documento", "Documentation": "Documentación", "Documents": "Documentos", "does not make any external connections, and your data stays securely on your locally hosted server.": "no realiza ninguna conexión externa y sus datos permanecen seguros en su servidor alojado localmente.", "Don't have an account?": "¿No tienes una cuenta?", "don't install random functions from sources you don't trust.": "no instale funciones aleatorias desde fuentes que no confíe.", "don't install random tools from sources you don't trust.": "no instale herramientas aleatorias desde fuentes que no confíe.", "Done": "<PERSON><PERSON>", "Download": "<PERSON><PERSON><PERSON>", "Download canceled": "Descarga cancelada", "Download Database": "Descarga la Base de Datos", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "Suelta cualquier archivo aquí para agregarlo a la conversación", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "p.ej. '30s','10m'. Unidades válidas de tiempo son 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "<PERSON><PERSON>", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "<PERSON><PERSON>", "Edit User": "<PERSON><PERSON>", "Edit User Group": "", "ElevenLabs": "", "Email": "Email", "Embark on adventures": "", "Embedding Batch Size": "Tamaño de Embedding", "Embedding Model": "Modelo de Embedding", "Embedding Model Engine": "Motor de Modelo de Embedding", "Embedding model set to \"{{embedding_model}}\"": "Modelo de Embedding configurado a \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Habilitar el uso compartido de la comunidad", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "Habilitar la calificación de los mensajes", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Habilitar Nuevos Registros", "Enable Web Search": "Habilitar la búsqueda web", "Enabled": "Activado", "Engine": "Motor", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Asegúrese de que su archivo CSV incluya 4 columnas en este orden: Nombre, Correo Electrónico, Contraseña, Rol.", "Enter {{role}} message here": "Ingrese el mensaje {{role}} aquí", "Enter a detail about yourself for your LLMs to recall": "Ingrese un detalle sobre usted para que sus LLMs recuerden", "Enter api auth string (e.g. username:password)": "Ingrese la cadena de autorización de api (p.ej., nombre:contraseña)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Ingresa la clave de API de Brave Search", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "Ingresa la escala de CFG (p.ej., 7.0)", "Enter Chunk Overlap": "Ingresar superposición de fragmentos", "Enter Chunk Size": "Ingrese el tamaño del fragmento", "Enter description": "", "Enter Github Raw URL": "Ingresa la URL sin procesar de Github", "Enter Google PSE API Key": "Ingrese la clave API de Google PSE", "Enter Google PSE Engine Id": "Introduzca el ID del motor PSE de Google", "Enter Image Size (e.g. 512x512)": "Ingrese el tamaño de la imagen (p.ej. 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "Ingrese códigos de idioma", "Enter Model ID": "Ingresa el ID del modelo", "Enter model tag (e.g. {{modelTag}})": "Ingrese la etiqueta del modelo (p.ej. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Ingrese el número de pasos (p.ej., 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "Ingrese el sampler (p.ej., Euler a)", "Enter Scheduler (e.g. Karras)": "Ingrese el planificador (p.ej., Ka<PERSON><PERSON>)", "Enter Score": "Ingrese la puntuación", "Enter SearchApi API Key": "Ingrese la Clave API de SearchApi", "Enter SearchApi Engine": "Ingrese el motor de SearchApi", "Enter Searxng Query URL": "Introduzca la URL de consulta de Searxng", "Enter Seed": "", "Enter Serper API Key": "Ingrese la clave API de Serper", "Enter Serply API Key": "Ingrese la clave API de Serply", "Enter Serpstack API Key": "Ingrese la clave API de Serpstack", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "Ingrese la secuencia de parada", "Enter system prompt": "Ingrese el prompt del sistema", "Enter Tavily API Key": "Ingrese la clave API de Tavily", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "Ingrese la URL del servidor Tika", "Enter Top K": "Ingrese el Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "Ingrese la URL (p.ej., http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Ingrese la URL (p.ej., http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "Ingrese su correo electrónico", "Enter Your Full Name": "Ingrese su nombre completo", "Enter your message": "Ingrese su mensaje", "Enter your new password": "", "Enter Your Password": "Ingrese su contraseña", "Enter your prompt": "", "Enter Your Role": "Ingrese su rol", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "Error", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "Experimental", "Explore the cosmos": "", "Export": "Exportar", "Export All Archived Chats": "", "Export All Chats (All Users)": "Exportar todos los chats (Todos los usuarios)", "Export chat (.json)": "Exportar chat (.json)", "Export Chats": "Exportar Chats", "Export Config to JSON File": "", "Export Functions": "Exportar Funciones", "Export Models": "Exportar Modelos", "Export Presets": "", "Export Prompts": "Exportar Prompts", "Export to CSV": "", "Export Tools": "Exportar Herramientas", "External Models": "Modelos Externos", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "No se pudo crear la clave API.", "Failed to read clipboard contents": "No se pudo leer el contenido del portapapeles", "Failed to save models configuration": "", "Failed to update settings": "Falla al actualizar los ajustes", "February": "<PERSON><PERSON><PERSON>", "Feedback History": "", "Feedbacks": "", "File": "Archivo", "File added successfully.": "Archivo agregado correctamente.", "File content updated successfully.": "Contenido del archivo actualizado correctamente.", "File Mode": "Modo de archivo", "File not found.": "Archivo no encontrado.", "File removed successfully.": "Archivo eliminado correctamente.", "File size should not exceed {{maxSize}} MB.": "Tamaño del archivo no debe exceder {{maxSize}} MB.", "File uploaded successfully": "", "Files": "Archivos", "Filter is now globally disabled": "El filtro ahora está desactivado globalmente", "Filter is now globally enabled": "El filtro ahora está habilitado globalmente", "Filters": "<PERSON><PERSON><PERSON>", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Se detectó suplantación de huellas: No se pueden usar las iniciales como avatar. Por defecto se utiliza la imagen de perfil predeterminada.", "Fluidly stream large external response chunks": "Transmita con fluidez grandes fragmentos de respuesta externa", "Focus chat input": "Enfoca la entrada del chat", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "De", "Format your variables using brackets like this:": "", "Frequency Penalty": "Penalización de frecuencia", "Function": "", "Function created successfully": "Función creada exitosamente", "Function deleted successfully": "Función borrada exitosamente", "Function Description": "", "Function ID": "", "Function is now globally disabled": "La función ahora está desactivada globalmente", "Function is now globally enabled": "La función está habilitada globalmente", "Function Name": "", "Function updated successfully": "Función actualizada exitosamente", "Functions": "Funciones", "Functions allow arbitrary code execution": "Funciones habilitan la ejecución de código arbitrario", "Functions allow arbitrary code execution.": "Funciones habilitan la ejecución de código arbitrario.", "Functions imported successfully": "Funciones importadas exitosamente", "General": "General", "General Settings": "Opciones Generales", "Generate Image": "Generar imagen", "Generating search query": "Generación de consultas de búsqueda", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "Global", "Good Response": "Buena Respuesta", "Google Drive": "", "Google PSE API Key": "Clave API de Google PSE", "Google PSE Engine Id": "ID del motor PSE de Google", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "GSA Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "h:mm a": "h:mm a", "Haptic Feedback": "Retroalimentación háptica", "Harmful or offensive": "", "has no conversations.": "no tiene conversaciones.", "Hello, {{name}}": "<PERSON><PERSON>, {{name}}", "Help": "<PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "Esconder", "Host": "", "How can I help you today?": "¿Cómo puedo ayudarte hoy?", "How would you rate this response?": "", "Hybrid Search": "Búsqueda Híbrida", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "<PERSON><PERSON><PERSON> que he leído y entiendo las implicaciones de mi acción. Estoy consciente de los riesgos asociados con la ejecución de código arbitrario y he verificado la confianza de la fuente.", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "Generación de imágenes (experimental)", "Image Generation Engine": "Motor de generación de imágenes", "Image Max Compression Size": "", "Image Settings": "Ajustes de la Imágen", "Images": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Import Chats": "Importar chats", "Import Config from JSON File": "", "Import Functions": "Importar Funciones", "Import Models": "Importar modelos", "Import Presets": "", "Import Prompts": "Importar Prompts", "Import Tools": "Importar <PERSON>", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "Incluir el indicador `--api-auth` al ejecutar stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Incluir el indicador `--api` al ejecutar stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "Información", "Input commands": "Ingresar comandos", "Install from Github URL": "Instalar desde la URL de Github", "Instant Auto-Send After Voice Transcription": "Auto-Enviar Después de la Transcripción de Voz", "Interface": "Interfaz", "Invalid file format.": "", "Invalid Tag": "Etiqueta <PERSON>álid<PERSON>", "is typing...": "", "January": "<PERSON><PERSON>", "Jina API Key": "", "join our Discord for help.": "Únase a nuestro Discord para obtener ayuda.", "JSON": "JSON", "JSON Preview": "Vista previa de JSON", "July": "<PERSON>", "June": "<PERSON><PERSON>", "JWT Expiration": "Expiración del JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "", "Keep Alive": "Mantener Vivo", "Key": "", "Keyboard shortcuts": "Atajos de teclado", "Knowledge": "Conocimiento", "Knowledge Access": "", "Knowledge created successfully.": "Conocimiento creado exitosamente.", "Knowledge deleted successfully.": "Conocimiento eliminado exitosamente.", "Knowledge reset successfully.": "Conocimiento restablecido exitosamente.", "Knowledge updated successfully": "Conocimiento actualizado exitosamente.", "Label": "", "Landing Page Mode": "Modo de Página de Inicio", "Language": "Lenguaje", "Last Active": "Última Actividad", "Last Modified": "Modificado por última vez", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "Deje vacío para ilimitado", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "Deje vacío para usar el propmt predeterminado, o ingrese un propmt personalizado", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "Escuchando...", "Local": "", "Local Models": "Modelos locales", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "Hecho por la comunidad de OpenWebUI", "Make sure to enclose them with": "Asegúrese de adjuntarlos con", "Make sure to export a workflow.json file as API format from ComfyUI.": "Asegúrese de exportar un archivo workflow.json en formato API desde ComfyUI.", "Manage": "Gestionar", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Administrar Pipelines", "March": "<PERSON><PERSON>", "Max Tokens (num_predict)": "<PERSON><PERSON><PERSON><PERSON> de fi<PERSON> (num_predict)", "Max Upload Count": "Cantidad máxima de cargas", "Max Upload Size": "Tamaño máximo de Cargas", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Se pueden descargar un máximo de 3 modelos simultáneamente. Por favor, inténtelo de nuevo más tarde.", "May": "Mayo", "Memories accessible by LLMs will be shown here.": "Las memorias accesibles por los LLMs se mostrarán aquí.", "Memory": "Memoria", "Memory added successfully": "Memoria añadida correctamente", "Memory cleared successfully": "Memoria liberada correctamente", "Memory deleted successfully": "Memoria borrada correctamente", "Memory updated successfully": "Memoria actualizada correctamente", "Merge Responses": "Fusionar Respuestas", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Los mensajes que envíe después de crear su enlace no se compartirán. Los usuarios con el enlace podrán ver el chat compartido.", "Min P": "", "Minimum Score": "Puntuación mínima", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "MMMM DD, YYYY hh:mm:ss A", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "El modelo '{{modelName}}' se ha descargado correctamente.", "Model '{{modelTag}}' is already in queue for downloading.": "El modelo '{{modelTag}}' ya está en cola para descargar.", "Model {{modelId}} not found": "El modelo {{modelId}} no fue encontrado", "Model {{modelName}} is not vision capable": "El modelo {{modelName}} no es capaz de ver", "Model {{name}} is now {{status}}": "El modelo {{name}} ahora es {{status}}", "Model accepts image inputs": "El modelo acepta entradas de imagenes", "Model created successfully!": "Modelo creado correctamente!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Se detectó la ruta del sistema de archivos del modelo. Se requiere el nombre corto del modelo para la actualización, no se puede continuar.", "Model Filtering": "", "Model ID": "ID del modelo", "Model IDs": "", "Model Name": "", "Model not selected": "Modelo no seleccionado", "Model Params": "Parámetros del modelo", "Model Permissions": "", "Model updated successfully": "Modelo actualizado correctamente", "Modelfile Content": "Contenido del Modelfile", "Models": "Modelos", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "Más", "Name": "Nombre", "Name your knowledge base": "", "New Chat": "Nuevo Chat", "New folder": "", "New Password": "Nueva Contraseña", "new-channel": "", "No content found": "", "No content to speak": "No hay contenido para hablar", "No distance available": "", "No feedbacks found": "", "No file selected": "Ningún archivo fué seleccionado", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "No se encontró contenido HTML, CSS, o JavaScript.", "No knowledge found": "No se encontró ningún conocimiento", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "No se han encontrado resultados", "No search query generated": "No se ha generado ninguna consulta de búsqueda", "No source available": "No hay fuente disponible", "No users were found.": "", "No valves to update": "No valves para actualizar", "None": "<PERSON><PERSON><PERSON>", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Nota: Si estableces una puntuación mínima, la búsqueda sólo devolverá documentos con una puntuación mayor o igual a la puntuación mínima.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Notificaciones", "November": "Noviembre", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "OAuth ID", "October": "Octubre", "Off": "Desactivado", "Okay, Let's Go!": "<PERSON><PERSON>, ¡Vamos!", "OLED Dark": "OLED oscuro", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "API de Ollama deshabilitada", "Ollama API settings updated": "", "Ollama Version": "Versión de Ollama", "On": "Activado", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "<PERSON><PERSON><PERSON> se <PERSON>en caracteres alfanuméricos y guiones en la cadena de comando.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Solo se pueden editar las colecciones, crear una nueva base de conocimientos para editar / añadir documentos", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "¡Ups! Parece que la URL no es válida. Vuelva a verificar e inténtelo nuevamente.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "¡Ups! Estás utilizando un método no compatible (solo frontend). Por favor ejecute la WebUI desde el backend.", "Open in full screen": "Abrir en pantalla completa", "Open new chat": "Abrir nuevo chat", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "La versión de Open WebUI (v{{OPEN_WEBUI_VERSION}}) es inferior a la versión requerida (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API Config", "OpenAI API Key is required.": "La Clave de la API de OpenAI es requerida.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "URL/Clave de OpenAI es requerida.", "or": "o", "Organize your users": "", "OUTPUT": "", "Output format": "Formato de salida", "Overview": "Vista general", "page": "p<PERSON><PERSON><PERSON>", "Password": "Contraseña", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF document (.pdf)", "PDF Extract Images (OCR)": "Extraer imágenes de PDF (OCR)", "pending": "pendiente", "Permission denied when accessing media devices": "Permiso denegado al acceder a los dispositivos", "Permission denied when accessing microphone": "Permiso denegado al acceder a la micrófono", "Permission denied when accessing microphone: {{error}}": "Permiso denegado al acceder al micrófono: {{error}}", "Permissions": "", "Personalization": "Personalización", "Pin": "<PERSON><PERSON>", "Pinned": "<PERSON><PERSON><PERSON>", "Pioneer insights": "", "Pipeline deleted successfully": "Pipeline borrada exitosamente", "Pipeline downloaded successfully": "Pipeline descargada exitosamente", "Pipelines": "Pipelines", "Pipelines Not Detected": "Pipeline No Detectada", "Pipelines Valves": "Tuberías Válvulas", "Plain text (.txt)": "Texto plano (.txt)", "Playground": "Patio de <PERSON>", "Please carefully review the following warnings:": "Por favor revise con cuidado los siguientes avisos:", "Please enter a prompt": "", "Please fill in all fields.": "Por favor llene todos los campos.", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "Últimos 30 días", "Previous 7 days": "Últimos 7 días", "Profile Image": "Imagen de perfil", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (por ejemplo, cuéntame una cosa divertida sobre el Imperio Romano)", "Prompt Content": "Contenido del Prompt", "Prompt created successfully": "", "Prompt suggestions": "Sugerencias de Prompts", "Prompt updated successfully": "", "Prompts": "Prompts", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "Extraer \"{{searchValue}}\" de Ollama.com", "Pull a model from Ollama.com": "Obtener un modelo de Ollama.com", "Query Generation Prompt": "", "Query Params": "Parámetros de consulta", "RAG Template": "Plantilla de RAG", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "<PERSON><PERSON> al oído", "Record voice": "Grabar voz", "Redirecting you to OpenWebUI Community": "Redireccionándote a la comunidad OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Referirse a usted mismo como \"Usuario\" (por ejemplo, \"El usuario está aprendiendo Español\")", "References from": "", "Refresh Token Expiration": "", "Regenerate": "<PERSON><PERSON><PERSON>", "Release Notes": "Notas de la versión", "Relevance": "", "Remove": "Eliminar", "Remove Model": "Eliminar modelo", "Rename": "Renombrar", "Reorder Models": "", "Repeat Last N": "Repetir las últimas N", "Reply in Thread": "", "Request Mode": "Modo de petición", "Reranking Model": "<PERSON><PERSON>rank<PERSON>", "Reranking model disabled": "Modelo de reranking deshabilitado", "Reranking model set to \"{{reranking_model}}\"": "Modelo de reranking establecido en \"{{reranking_model}}\"", "Reset": "Reiniciar", "Reset All Models": "", "Reset Upload Directory": "Reiniciar Directorio de carga", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Las notificaciones de respuesta no pueden activarse debido a que los permisos del sitio web han sido denegados. Por favor, visite las configuraciones de su navegador para otorgar el acceso necesario.", "Response splitting": "División de respuestas", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "Rol", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "<PERSON><PERSON><PERSON><PERSON>", "Running": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Save": "Guardar", "Save & Create": "Guardar y Crear", "Save & Update": "Guardar y Actualizar", "Save As Copy": "Guardar como copia", "Save Tag": "Guardar etiqueta", "Saved": "Guardado", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Ya no se admite guardar registros de chat directamente en el almacenamiento de su navegador. Tómese un momento para descargar y eliminar sus registros de chat haciendo clic en el botón a continuación. No te preocupes, puedes volver a importar fácilmente tus registros de chat al backend a través de", "Scroll to bottom when switching between branches": "Moverse a la parte inferior cuando se cambia entre ramas", "Search": "Buscar", "Search a model": "Buscar un modelo", "Search Base": "", "Search Chats": "Chats de búsqueda", "Search Collection": "<PERSON><PERSON>", "Search Filters": "", "search for tags": "", "Search Functions": "Buscar Funciones", "Search Knowledge": "Buscar Conocimiento", "Search Models": "Buscar Modelos", "Search options": "", "Search Prompts": "Buscar Prompts", "Search Result Count": "Recuento de resultados de búsqueda", "Search Tools": "Búsqueda de herramientas", "Search users": "", "SearchApi API Key": "Clave API de SearchApi", "SearchApi Engine": "Motor de SearchApi", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "Buscando \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Buscando Conocimiento para \"{{searchQuery}}\"", "Searxng Query URL": "Searxng URL de consulta", "See readme.md for instructions": "Vea el readme.md para instrucciones", "See what's new": "Ver las novedades", "Seed": "Seed", "Select a base model": "Seleccionar un modelo base", "Select a engine": "Busca un motor", "Select a function": "Busca una función", "Select a group": "", "Select a model": "Selecciona un modelo", "Select a pipeline": "Selección de una Pipeline", "Select a pipeline url": "Selección de una dirección URL de Pipeline", "Select a tool": "Busca una herramienta", "Select Engine": "Selecciona Motor", "Select Knowledge": "Selecciona Conocimiento", "Select model": "Selecciona un modelo", "Select only one model to call": "Selecciona sólo un modelo para llamar", "Selected model(s) do not support image inputs": "Los modelos seleccionados no admiten entradas de imagen", "Semantic distance to query": "", "Send": "Enviar", "Send a message": "", "Send a Message": "Enviar un Mensaje", "Send message": "<PERSON><PERSON><PERSON>", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Envia `stream_options: { include_usage: true }` en la solicitud.\nLos proveedores admitidos devolverán información de uso del token en la respuesta cuando se establezca.", "September": "Septiembre", "Serper API Key": "Clave API de Serper", "Serply API Key": "Clave API de Serply", "Serpstack API Key": "Clave API de Serpstack", "Server connection verified": "Conexión del servidor verificada", "Set as default": "<PERSON><PERSON><PERSON> por defecto", "Set CFG Scale": "Establecer la escala CFG", "Set Default Model": "Establecer modelo predeterminado", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Establecer modelo de embedding (ej. {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON> de <PERSON>n", "Set reranking model (e.g. {{model}})": "Establecer modelo de reranking (ej. {{model}})", "Set Sampler": "<PERSON><PERSON><PERSON>", "Set Scheduler": "Establecer Programador", "Set Steps": "E<PERSON><PERSON>", "Set Task Model": "Establecer modelo de <PERSON>rea", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "E<PERSON><PERSON> la voz", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Configuración", "Settings saved successfully!": "¡Configuración guardada con éxito!", "Share": "Compartir", "Share Chat": "Compartir <PERSON>", "Share to OpenWebUI Community": "Compartir con la comunidad OpenWebUI", "Show": "Mostrar", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "Mostrar detalles de administración en la capa de espera de la cuenta", "Show shortcuts": "<PERSON><PERSON>", "Show your support!": "¡Muestra tu apoyo!", "Sign in": "In<PERSON><PERSON>", "Sign in to {{WEBUI_NAME}}": "Iniciar <PERSON><PERSON><PERSON> en {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "<PERSON><PERSON><PERSON>", "Sign up": "<PERSON><PERSON><PERSON> una cuenta", "Sign up to {{WEBUI_NAME}}": "Crear una cuenta en {{WEBUI_NAME}}", "Signing in to {{WEBUI_NAME}}": "Iniciando sesi<PERSON> en {{WEBUI_NAME}}", "sk-1234": "", "Source": "Fuente", "Speech Playback Speed": "Velocidad de reproducción de voz", "Speech recognition error: {{error}}": "Error de reconocimiento de voz: {{error}}", "Speech-to-Text Engine": "Motor de voz a texto", "Stop": "", "Stop Sequence": "Detener secuencia", "Stream Chat Response": "", "STT Model": "Modelo STT", "STT Settings": "Configuraciones de STT", "Success": "Éxito", "Successfully updated.": "Actualizado exitosamente.", "Suggested prompts to get you started": "", "Support": "Soporte", "Support this plugin:": "Brinda soporte a este plugin:", "Sync directory": "Sincroniza directorio", "System": "Sistema", "System Instructions": "", "System Prompt": "Prompt del sistema", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "Toca para interrumpir", "Tavily API Key": "Clave API de Tavily", "Temperature": "Temperatura", "Template": "Plantilla", "Temporary Chat": "Chat temporal", "Text Splitter": "", "Text-to-Speech Engine": "Motor de texto a voz", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "¡Grac<PERSON> por tu retroalimentación!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Los desarrolladores de este plugin son apasionados voluntarios de la comunidad. Si encuentras este plugin útil, por favor considere contribuir a su desarrollo.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "El tamaño máximo del archivo en MB. Si el tamaño del archivo supera este límite, el archivo no se subirá.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "El número máximo de archivos que se pueden utilizar a la vez en chat. Si este límite es superado, los archivos no se subirán.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "El puntaje debe ser un valor entre 0.0 (0%) y 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "<PERSON><PERSON>", "Thinking...": "Pensando...", "This action cannot be undone. Do you wish to continue?": "Esta acción no se puede deshacer. ¿Desea continuar?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Esto garantiza que sus valiosas conversaciones se guarden de forma segura en su base de datos en el backend. ¡Gracias!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Esta es una característica experimental que puede no funcionar como se esperaba y está sujeto a cambios en cualquier momento.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": " Esta opción eliminará todos los archivos existentes en la colección y los reemplazará con nuevos archivos subidos.", "This response was generated by \"{{model}}\"": "", "This will delete": "Esto eliminará", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Esto reseteará la base de conocimientos y sincronizará todos los archivos. ¿Desea continuar?", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "URL del servidor de Tika", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Consejo: Actualice múltiples variables consecutivamente presionando la tecla tab en la entrada del chat después de cada reemplazo.", "Title": "<PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "Título (por ejemplo, cuéntame una curiosidad)", "Title Auto-Generation": "Generación automática de títulos", "Title cannot be an empty string.": "El título no puede ser una cadena vacía.", "Title Generation Prompt": "Prompt de generación de título", "TLS": "", "To access the available model names for downloading,": "Para acceder a los nombres de modelos disponibles para descargar,", "To access the GGUF models available for downloading,": "Para acceder a los modelos GGUF disponibles para descargar,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Para acceder al interfaz de usuario web, por favor contacte al administrador. Los administradores pueden administrar los estados de los usuarios desde el panel de administración.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Para adjuntar la base de conocimientos aquí, agreguelas al área de trabajo \"Conocimiento\" primero.", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "Para seleccionar acciones aquí, agreguelas al área de trabajo \"Funciones\" primero.", "To select filters here, add them to the \"Functions\" workspace first.": "Para seleccionar filtros aquí, agreguelos al área de trabajo \"Funciones\" primero.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Para seleccionar herramientas aquí, agreguelas al área de trabajo \"Herramientas\" primero.", "Toast notifications for new updates": "", "Today": "Hoy", "Toggle settings": "Alternar configuración", "Toggle sidebar": "Alternar barra lateral", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "Tokens a mantener en el contexto de actualización (num_keep)", "Tool created successfully": "Herramienta creada con éxito", "Tool deleted successfully": "Herramienta eliminada con éxito", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "Herramienta importada con éxito", "Tool Name": "", "Tool updated successfully": "Herramienta actualizada con éxito", "Tools": "Herramientas", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "Las herramientas son un sistema de llamada de funciones con código arbitrario", "Tools have a function calling system that allows arbitrary code execution": "Las herramientas tienen un sistema de llamadas de funciones que permite la ejecución de código arbitrario", "Tools have a function calling system that allows arbitrary code execution.": "Las herramientas tienen un sistema de llamada de funciones que permite la ejecución de código arbitrario.", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "¿Problemas para acceder a Ollama?", "TTS Model": "Modelo TTS", "TTS Settings": "Configuración de TTS", "TTS Voice": "Voz del TTS", "Type": "Tipo", "Type Hugging Face Resolve (Download) URL": "Escriba la URL (Descarga) de Hugging Face Resolve", "Uh-oh! There was an issue with the response.": "", "UI": "UI", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "Desanclar", "Unravel secrets": "", "Untagged": "", "Update": "Actualizar", "Update and Copy Link": "Actualizar y copiar enlace", "Update for the latest features and improvements.": "Actualize para las últimas características e mejoras.", "Update password": "Actualizar contraseña", "Updated": "", "Updated at": "Actualizado en", "Updated At": "", "Upload": "Subir", "Upload a GGUF model": "Subir un modelo GGUF", "Upload directory": "Directorio de carga", "Upload files": "Subir archivos", "Upload Files": "Subir archivos", "Upload Pipeline": "Subir Pipeline", "Upload Progress": "Progreso de carga", "URL": "", "URL Mode": "Modo de URL", "Use '#' in the prompt input to load and include your knowledge.": "Utilize '#' en el prompt para cargar y incluir su conocimiento.", "Use Gravatar": "<PERSON><PERSON>", "Use groups to group your users and assign permissions.": "", "Use Initials": "Usar Iniciales", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "usuario", "User": "", "User location successfully retrieved.": "Localización del usuario recuperada con éxito.", "Username": "", "Users": "Usuarios", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "<PERSON><PERSON><PERSON><PERSON>", "Valid time units:": "Unidades válidas de tiempo:", "Valves": "Valves", "Valves updated": "<PERSON>ves actualizados", "Valves updated successfully": "Valves actualizados con éxito", "variable": "variable", "variable to have them replaced with clipboard content.": "variable para reemplazarlos con el contenido del portapapeles.", "Version": "Versión", "Version {{selectedVersion}} of {{totalVersions}}": "Versión {{selectedVersion}} de {{totalVersions}}", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "Voz", "Voice Input": "", "Warning": "Advertencia", "Warning:": "Advertencia:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Advertencia: Si actualiza o cambia su modelo de inserción, necesitará volver a importar todos los documentos.", "Web": "Web", "Web API": "API Web", "Web Loader Settings": "Web Loader Settings", "Web Search": "Búsqueda en la Web", "Web Search Engine": "Motor de búsqueda web", "Web Search Query Generation": "", "Webhook URL": "Webhook URL", "WebUI Settings": "Configuración del WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "Novedades en", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "<PERSON><PERSON><PERSON> (Local)", "Widescreen Mode": "<PERSON><PERSON> de pan<PERSON> ancha", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "Espacio de trabajo", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "Escribe una sugerencia para un prompt (por e<PERSON><PERSON><PERSON>, ¿quién eres?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Escribe un resumen en 50 palabras que resuma [tema o palabra clave].", "Write something...": "", "Write your model template content here": "", "Yesterday": "Ayer", "You": "Usted", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Puede personalizar sus interacciones con LLMs añadiendo memorias a través del botón 'Gestionar' debajo, haciendo que sean más útiles y personalizados para usted.", "You cannot upload an empty file.": "", "You have no archived conversations.": "No tiene conversaciones archivadas.", "You have shared this chat": "Usted ha compartido esta conversación", "You're a helpful assistant.": "Usted es un asistente útil.", "Your account status is currently pending activation.": "El estado de su cuenta actualmente se encuentra pendiente de activación.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Su contribución completa irá directamente a el desarrollador del plugin; Open WebUI no toma ningun porcentaje. Sin embargo, la plataforma de financiación elegida podría tener sus propias tarifas.", "Youtube": "Youtube", "Youtube Loader Settings": "Configuración del cargador de Youtube"}