{"-1 for no limit, or a positive integer for a specific limit": "-1 per a cap límit, o un nombre positiu per a un límit específic", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' o '-1' perquè no caduqui mai.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(p. ex. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(p. ex. `sh webui.sh --api`)", "(latest)": "(últim)", "{{ models }}": "{{ models }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "Els xats de {{user}}", "{{webUIName}} Backend Required": "El Backend de {{webUIName}} és necessari", "*Prompt node ID(s) are required for image generation": "*Els identificadors de nodes d'indicacions són necessaris per a la generació d'imatges", "A new version (v{{LATEST_VERSION}}) is now available.": "Hi ha una nova versió disponible (v{{LATEST_VERSION}}).", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Un model de tasca s'utilitza quan es realitzen tasques com ara generar títols per a xats i consultes de cerca per a la web", "a user": "un usuari", "About": "Sobre", "Access": "Accés", "Access Control": "Control d'accés", "Accessible to all users": "Accessible a tots els usuaris", "Account": "<PERSON><PERSON><PERSON>", "Account Activation Pending": "Activació del compte pendent", "Actions": "Accions", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Activa aquest comanda escrivint \"{{COMMAND}}\" en el xat", "Active Users": "<PERSON><PERSON><PERSON> actius", "Add": "<PERSON><PERSON>gi<PERSON>", "Add a model ID": "Afegir un ID de model", "Add a short description about what this model does": "Afegeix una breu descripció sobre què fa aquest model", "Add a tag": "Afegir una etiqueta", "Add Arena Model": "Afegir model de l'Arena", "Add Connection": "Afegir connexió", "Add Content": "Afegir contingut", "Add content here": "Afegir contingut aquí", "Add custom prompt": "Afegir una indicació personalitzada", "Add Files": "<PERSON><PERSON><PERSON><PERSON> arxius", "Add Group": "Afegir grup", "Add Memory": "<PERSON><PERSON><PERSON><PERSON>", "Add Model": "Afegir un model", "Add Reaction": "", "Add Tag": "Afegir etiqueta", "Add Tags": "Afegir etiquetes", "Add text content": "Afegir contingut de text", "Add User": "Afegir un usuari", "Add User Group": "Afegir grup d'usuaris", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Si ajustes aquesta preferència, els canvis s'aplicaran de manera universal a tots els usuaris.", "admin": "administrador", "Admin": "Administrador", "Admin Panel": "Panell d'administració", "Admin Settings": "Preferències d'administració", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Els administradors tenen accés a totes les eines en tot moment; els usuaris necessiten eines assignades per model a l'espai de treball.", "Advanced Parameters": "Paràmetres avançats", "Advanced Params": "Paràmetres avançats", "All Documents": "Tots els documents", "All models deleted successfully": "<PERSON><PERSON> els models s'han eliminat correctament", "Allow Chat Delete": "Permetre eliminar el xat", "Allow Chat Deletion": "Permetre la supressió del xat", "Allow Chat Edit": "Permetre editar el xat", "Allow File Upload": "Permetre la pujada d'arxius", "Allow non-local voices": "Permetre veus no locals", "Allow Temporary Chat": "Permetre el xat temporal", "Allow User Location": "Permetre la ubicació de l'usuari", "Allow Voice Interruption in Call": "Permetre la interrupció de la veu en una trucada", "Allowed Endpoints": "", "Already have an account?": "Ja tens un compte?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "Alternativa al top_p, i pretén garantir un equilibri de qualitat i varietat. El paràmetre p representa la probabilitat mínima que es consideri un token, en relació amb la probabilitat del token més probable. Per exemple, amb p=0,05 i el token més probable amb una probabilitat de 0,9, es filtren els logits amb un valor inferior a 0,045. (Per defecte: 0.0)", "an assistant": "un assistent", "and": "i", "and {{COUNT}} more": "i {{COUNT}} més", "and create a new shared link.": "i crear un nou enllaç compartit.", "API Base URL": "URL Base de l'API", "API Key": "clau <PERSON>", "API Key created.": "clau <PERSON> c<PERSON>a.", "API Key Endpoint Restrictions": "", "API keys": "Claus de l'API", "Application DN": "DN d'aplicació", "Application DN Password": "Contrasenya del DN d'aplicació", "applies to all users with the \"user\" role": "s'aplica a tots els usuaris amb el rol \"usuari\"", "April": "Abril", "Archive": "<PERSON><PERSON><PERSON><PERSON>", "Archive All Chats": "Arxiva tots els xats", "Archived Chats": "Xats arxivats", "archived-chat-export": "archived-chat-export", "Are you sure you want to delete this channel?": "Estàs segur que vols eliminar aquest canal?", "Are you sure you want to delete this message?": "Estàs segur que vols eliminar aquest missatge?", "Are you sure you want to unarchive all archived chats?": "Est<PERSON>s segur que vols desarxivar tots els xats arxivats?", "Are you sure?": "<PERSON><PERSON><PERSON><PERSON> segur?", "Arena Models": "Models de l'Arena", "Artifacts": "Artefactes", "Ask a question": "Fer una pregunta", "Assistant": "Assistent", "Attach file": "<PERSON><PERSON><PERSON> arxiu", "Attribute for Username": "Atribut per al Nom d'usuari", "Audio": "<PERSON><PERSON><PERSON>", "August": "Agost", "Authenticate": "Autenticar", "Auto-Copy Response to Clipboard": "Copiar la resposta automàticament al porta-retalls", "Auto-playback response": "Reproduir la resposta automàticament", "Autocomplete Generation": "Generació automàtica", "Autocomplete Generation Input Max Length": "Entrada màxima de la generació automàtica", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "Cadena d'autenticació de l'API d'AUTOMATIC1111", "AUTOMATIC1111 Base URL": "URL Base d'AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "Es requereix l'URL Base d'AUTOMATIC1111.", "Available list": "Llista de disponibles", "available!": "disponible!", "Azure AI Speech": "Azure AI Speech", "Azure Region": "Regió <PERSON>", "Back": "<PERSON><PERSON><PERSON>", "Bad": "", "Bad Response": "Resposta errònia", "Banners": "Banners", "Base Model (From)": "Model base (des de)", "Batch Size (num_batch)": "Mida del lot (num_batch)", "before": "abans", "Beta": "Beta", "BETA": "", "Bing Search V7 Endpoint": "Punt de connexió a Bing Search V7", "Bing Search V7 Subscription Key": "Clau de subscripció a Bing Search V7", "Brave Search API Key": "Clau API de Brave Search", "By {{name}}": "Per {{name}}", "Bypass SSL verification for Websites": "Desactivar la verificació SSL per a l'accés a Internet", "Call": "Trucada", "Call feature is not supported when using Web STT engine": "La funció de trucada no s'admet quan s'utilitza el motor Web STT", "Camera": "Càmera", "Cancel": "Cancel·lar", "Capabilities": "Capacitats", "Capture": "Captura", "Certificate Path": "Camí del certificat", "Change Password": "Canviar la contrasenya", "Channel Name": "Nom del canal", "Channels": "Canals", "Character": "Personatge", "Character limit for autocomplete generation input": "Límit de caràcters per a l'entrada de generació automàtica", "Chart new frontiers": "Traça noves fronteres", "Chat": "Xat", "Chat Background Image": "Imatge de fons del xat", "Chat Bubble UI": "Chat Bubble UI", "Chat Controls": "Controls de xat", "Chat direction": "Direcció del xat", "Chat Overview": "Vista general del xat", "Chat Permissions": "Permisos del xat", "Chat Tags Auto-Generation": "Generació automàtica d'etiquetes del xat", "Chats": "Xats", "Check Again": "Comprovar-ho de nou", "Check for updates": "Comprovar si hi ha actualitzacions", "Checking for updates...": "Comprovant actualitzacions...", "Choose a model before saving...": "Triar un model abans de desar...", "Chunk Overlap": "Solapament de blocs", "Chunk Params": "Paràmetres dels blocs", "Chunk Size": "Mida del bloc", "Ciphers": "Xi<PERSON><PERSON><PERSON>", "Citation": "Cita", "Clear memory": "Esborrar la memòria", "click here": "prem aquí", "Click here for filter guides.": "Clica aquí per filtrar les guies.", "Click here for help.": "Clica aquí per obtenir ajuda.", "Click here to": "Clic aquí per", "Click here to download user import template file.": "Fes clic aquí per descarregar l'arxiu de plantilla d'importació d'usuaris", "Click here to learn more about faster-whisper and see the available models.": "Clica aquí per obtenir més informació sobre faster-whisper i veure els models disponibles.", "Click here to select": "Clica aquí per seleccionar", "Click here to select a csv file.": "Clica aquí per seleccionar un fitxer csv.", "Click here to select a py file.": "Clica aquí per seleccionar un fitxer py.", "Click here to upload a workflow.json file.": "Clica aquí per pujar un arxiu workflow.json", "click here.": "clica aquí.", "Click on the user role button to change a user's role.": "Clica sobre el botó de rol d'usuari per canviar el rol d'un usuari.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Permís d'escriptura al porta-retalls denegat. Comprova els ajustos de navegador per donar l'accés necessari.", "Clone": "Clonar", "Close": "<PERSON><PERSON>", "Code execution": "Execució de codi", "Code formatted successfully": "Codi formatat correctament", "Collection": "Col·lecció", "Color": "Color", "ComfyUI": "ComfyUI", "ComfyUI API Key": "Configurar la clau API de ComfyUI", "ComfyUI Base URL": "URL base de ComfyUI", "ComfyUI Base URL is required.": "L'URL base de ComfyUI és obligatòria.", "ComfyUI Workflow": "Flux de treball de ComfyUI", "ComfyUI Workflow Nodes": "Nodes del flux de treball de ComfyUI", "Command": "<PERSON><PERSON><PERSON>", "Completions": "Completaments", "Concurrent Requests": "Peticions simultànies", "Configure": "Configurar", "Configure Models": "Configurar models", "Confirm": "Confirmar", "Confirm Password": "Confirmar la contrasenya", "Confirm your action": "Confirma la teva acció", "Confirm your new password": "Confirma la teva nova contrasenya", "Connections": "Connexions", "Contact Admin for WebUI Access": "Posat en contacte amb l'administrador per accedir a WebUI", "Content": "Contingut", "Content Extraction": "Extracció de contingut", "Context Length": "Mida del context", "Continue Response": "Continuar la resposta", "Continue with {{provider}}": "Continuar amb {{provider}}", "Continue with Email": "Continuar amb el correu", "Continue with LDAP": "Continuar amb LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Controlar com es divideix el text del missatge per a les sol·licituds TTS. 'Puntuació' divideix en frases, 'paràgrafs' divideix en paràgrafs i 'cap' manté el missatge com una cadena única.", "Controls": "Controls", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "Controlar l'equilibri entre la coherència i la diversitat de la sortida. Un valor més baix donarà lloc a un text més enfocat i coherent. (Per defecte: 5.0)", "Copied": "Copiat", "Copied shared chat URL to clipboard!": "S'ha copiat l'URL compartida al porta-retalls!", "Copied to clipboard": "Copiat al porta-retalls", "Copy": "Copiar", "Copy last code block": "Copiar l'últim bloc de codi", "Copy last response": "Copiar l'última resposta", "Copy Link": "Copiar l'enllaç", "Copy to clipboard": "Copiar al porta-retalls", "Copying to clipboard was successful!": "La còpia al porta-retalls s'ha realitzat correctament", "Create": "<PERSON><PERSON><PERSON>", "Create a knowledge base": "Crear una base de coneixement", "Create a model": "Crear un model", "Create Account": "Crear un compte", "Create Admin Account": "Crear un compte d'Administrador", "Create Channel": "Crear un canal", "Create Group": "<PERSON><PERSON><PERSON> grup", "Create Knowledge": "<PERSON><PERSON>r Coneixement", "Create new key": "<PERSON>rear una nova clau", "Create new secret key": "<PERSON>rear una nova clau secreta", "Created at": "<PERSON>reat el", "Created At": "<PERSON>reat el", "Created by": "Creat per", "CSV Import": "Importar CSV", "Current Model": "Model actual", "Current Password": "Contrasenya actual", "Custom": "Personalitzat", "Dark": "Fosc", "Database": "Base de dades", "December": "Desembre", "Default": "Per defecte", "Default (Open AI)": "<PERSON> defecte (Open AI)", "Default (SentenceTransformers)": "Per defecte (SentenceTransformers)", "Default Model": "Model per defecte", "Default model updated": "Model per defecte actualitzat", "Default Models": "Models per defecte", "Default permissions": "<PERSON><PERSON><PERSON> per defecte", "Default permissions updated successfully": "Permisos per defecte actualitzats correctament", "Default Prompt Suggestions": "Suggeriments d'indicació per defecte", "Default to 389 or 636 if TLS is enabled": "Per defecte 389 o 636 si TLS està habilitat", "Default to ALL": "Per defecte TOTS", "Default User Role": "Rol d'usuari per defecte", "Delete": "Eliminar", "Delete a model": "Eliminar un model", "Delete All Chats": "Eliminar tots els xats", "Delete All Models": "Eliminar tots els models", "Delete chat": "Eliminar xat", "Delete Chat": "Eliminar xat", "Delete chat?": "Eliminar el xat?", "Delete folder?": "Eliminar la carpeta?", "Delete function?": "Eliminar funció?", "Delete Message": "Eleiminar el missatge", "Delete prompt?": "Eliminar indicació?", "delete this link": "Eliminar aquest enllaç", "Delete tool?": "Eliminar eina?", "Delete User": "Eliminar usuari", "Deleted {{deleteModelTag}}": "S'ha eliminat {{deleteModelTag}}", "Deleted {{name}}": "S'ha eliminat {{name}}", "Deleted User": "<PERSON><PERSON><PERSON> eliminat", "Describe your knowledge base and objectives": "Descriu la teva base de coneixement i objectius", "Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Disabled": "Deshabilitat", "Discover a function": "Descobrir una funció", "Discover a model": "Descobrir un model", "Discover a prompt": "Descobrir una indicació", "Discover a tool": "Descobrir una eina", "Discover wonders": "<PERSON><PERSON><PERSON><PERSON>", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON><PERSON>, descar<PERSON>gar i explorar funcions personalitzades", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON><PERSON>, descarregar i explorar indicacions personalitzades", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON><PERSON>, descar<PERSON><PERSON> i explorar eines personalitzades", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON><PERSON>, descarregar i explorar models preconfigurats", "Dismissible": "Descartable", "Display": "Mostrar", "Display Emoji in Call": "Mostrar emojis a la trucada", "Display the username instead of You in the Chat": "Mostrar el nom d'usuari en lloc de 'Tu' al xat", "Displays citations in the response": "Mostra les referències a la resposta", "Dive into knowledge": "Aprofundir en el coneixement", "Do not install functions from sources you do not fully trust.": "No instal·lis funcions de fonts en què no confiïs plenament.", "Do not install tools from sources you do not fully trust.": "No instal·lis eines de fonts en què no confiïs plenament.", "Document": "Document", "Documentation": "Documentació", "Documents": "Documents", "does not make any external connections, and your data stays securely on your locally hosted server.": "no realitza connexions externes, i les teves dades romanen segures al teu servidor allotjat localment.", "Don't have an account?": "No tens un compte?", "don't install random functions from sources you don't trust.": "no instal·lis funcions aleatòries de fonts en què no confiïs.", "don't install random tools from sources you don't trust.": "no instal·lis eines aleatòries de fonts en què no confiïs.", "Done": "Fet", "Download": "<PERSON><PERSON><PERSON><PERSON>", "Download canceled": "Descàrrega cancel·lada", "Download Database": "Descarregar la base de dades", "Drag and drop a file to upload or select a file to view": "Arrossegar un arxiu per pujar o escull un arxiu a veure", "Draw": "Dibuixar", "Drop any files here to add to the conversation": "Deixa qualsevol arxiu aquí per afegir-lo a la conversa", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "p. ex. '30s','10m'. Les unitats de temps vàlides són 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "p. ex. Un filtre per eliminar paraules malsonants del text", "e.g. My Filter": "p. ex. El meu filtre", "e.g. My Tools": "p. ex. Les meves eines", "e.g. my_filter": "p. ex. els_meus_filtres", "e.g. my_tools": "p. ex. les_meves_eines", "e.g. Tools for performing various operations": "p. ex. Eines per dur a terme operacions", "Edit": "<PERSON><PERSON>", "Edit Arena Model": "Editar model de l'Arena", "Edit Channel": "Editar el canal", "Edit Connection": "Editar la connexió", "Edit Default Permissions": "Editar el permisos per defecte", "Edit Memory": "Editar la memòria", "Edit User": "<PERSON>ar l'usuari", "Edit User Group": "Editar el grup d'usuaris", "ElevenLabs": "ElevenLabs", "Email": "<PERSON><PERSON>u elect<PERSON>ò<PERSON>", "Embark on adventures": "Embarcar en aventures", "Embedding Batch Size": "Mida del lot d'incrustació", "Embedding Model": "Model d'incrust<PERSON><PERSON>", "Embedding Model Engine": "Motor de model d'incrustació", "Embedding model set to \"{{embedding_model}}\"": "Model d'incrustació configurat a \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "Activar la generació automàtica per als missatges del xat", "Enable Community Sharing": "Activar l'ús compartit amb la comunitat", "Enable Google Drive": "Activar Google Drive", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Activar el bloqueig de memòria (mlock) per evitar que les dades del model s'intercanviïn fora de la memòria RAM. Aquesta opció bloqueja el conjunt de pàgines de treball del model a la memòria RAM, assegurant-se que no s'intercanviaran al disc. Això pot ajudar a mantenir el rendiment evitant errors de pàgina i garantint un accés ràpid a les dades.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Activar l'assignació de memòria (mmap) per carregar les dades del model. Aquesta opció permet que el sistema utilitzi l'emmagatzematge en disc com a extensió de la memòria RAM tractant els fitxers de disc com si estiguessin a la memòria RAM. Això pot millorar el rendiment del model permetent un accés més ràpid a les dades. Tanmateix, és possible que no funcioni correctament amb tots els sistemes i pot consumir una quantitat important d'espai en disc.", "Enable Message Rating": "Permetre la qualificació de missatges", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "Activar el mostreig de Mirostat per controlar la perplexitat. (Per defecte: 0, 0 = Inhabilitat, 1 = Mirostat, 2 = Mirostat 2.0)", "Enable New Sign Ups": "Permetre nous registres", "Enable Web Search": "Activar la cerca web", "Enabled": "Habilitat", "Engine": "Motor", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Assegura't que els teus fitxers CSV inclouen 4 columnes en aquest ordre: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Contrasenya, Rol.", "Enter {{role}} message here": "Introdueix aquí el missatge de {{role}}", "Enter a detail about yourself for your LLMs to recall": "Introdueix un detall sobre tu què els teus models de llenguatge puguin recordar", "Enter api auth string (e.g. username:password)": "Entra la cadena d'autenticació api (p. ex. nom d'usuari:contrasenya)", "Enter Application DN": "Introdueix el DN d'aplicació", "Enter Application DN Password": "Introdueix la contrasenya del DN d'aplicació", "Enter Bing Search V7 Endpoint": "Introdueix el punt de connexió de Bing Search V7", "Enter Bing Search V7 Subscription Key": "Introdueix la clau de subscripció de Bing Search V7", "Enter Brave Search API Key": "Introdueix la clau API de Brave Search", "Enter certificate path": "Introdueix el camí del certificat", "Enter CFG Scale (e.g. 7.0)": "Entra l'escala CFG (p.ex. 7.0)", "Enter Chunk Overlap": "Introdueix la mida de solapament de blocs", "Enter Chunk Size": "Introdueix la mida del bloc", "Enter description": "Introdueix la descripció", "Enter Github Raw URL": "Introdueix l'URL en brut de Github", "Enter Google PSE API Key": "Introdueix la clau API de Google PSE", "Enter Google PSE Engine Id": "Introdueix l'identificador del motor PSE de Google", "Enter Image Size (e.g. 512x512)": "Introdueix la mida de la imatge (p. ex. 512x512)", "Enter Jina API Key": "Introdueix la clau API de Jin<PERSON>", "Enter Kagi Search API Key": "Introdueix la clau API de Kagi Search", "Enter language codes": "Introdueix els codis de llenguatge", "Enter Model ID": "Introdueix l'identificador del model", "Enter model tag (e.g. {{modelTag}})": "Introdueix l'etiqueta del model (p. ex. {{modelTag}})", "Enter Mojeek Search API Key": "Introdueix la clau API de Mojeek Search", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Introdueix el nombre de passos (p. ex. 50)", "Enter proxy URL (e.g. **************************:port)": "Entra l'URL (p. ex. **************************:port)", "Enter Sampler (e.g. Euler a)": "Introdueix el mostrejador (p.ex. Euler a)", "Enter Scheduler (e.g. Karras)": "Entra el programador (p.ex. <PERSON>)", "Enter Score": "Introdueix la puntuació", "Enter SearchApi API Key": "Introdueix la clau API SearchApi", "Enter SearchApi Engine": "Introdueix el motor SearchApi", "Enter Searxng Query URL": "Introdueix l'URL de consulta de Searxng", "Enter Seed": "Introdueix la llavor", "Enter Serper API Key": "Introdueix la clau API <PERSON>", "Enter Serply API Key": "Introdueix la clau API Serply", "Enter Serpstack API Key": "Introdueix la clau API <PERSON>", "Enter server host": "Introdueix el servidor", "Enter server label": "Introdueix l'etiqueta del servidor", "Enter server port": "Introdueix el port del servidor", "Enter stop sequence": "Introdueix la seqüència de parada", "Enter system prompt": "Introdueix la indicació de sistema", "Enter Tavily API Key": "Introdueix la clau API <PERSON>", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Entra la URL pública de WebUI. Aquesta URL s'utilitzarà per generar els enllaços en les notificacions.", "Enter Tika Server URL": "Introdueix l'URL del servidor Tika", "Enter Top K": "Introdueix Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "Introdueix l'URL (p. ex. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Introdueix l'URL (p. ex. http://localhost:11434)", "Enter your current password": "Introdueix la teva contrasenya actual", "Enter Your Email": "Introdueix el teu correu electrònic", "Enter Your Full Name": "Introdueix el teu nom complet", "Enter your message": "Introdueix el teu missatge", "Enter your new password": "Introdueix la teva nova contrasenya", "Enter Your Password": "Introdueix la teva contrasenya", "Enter your prompt": "", "Enter Your Role": "Introdueix el teu rol", "Enter Your Username": "Introdueix el teu nom d'usuari", "Enter your webhook URL": "Entra la URL del webhook", "Error": "Error", "ERROR": "ERROR", "Error accessing Google Drive: {{error}}": "Error en accedir a Google Drive: {{error}}", "Error uploading file: {{error}}": "Error en pujar l'arxiu: {{error}}", "Evaluations": "Avaluacions", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Exemple: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Exemple: TOTS", "Example: ou=users,dc=foo,dc=example": "Exemple: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Exemple: sAMAccountName o uid o userPrincipalName", "Exclude": "Ex<PERSON>loure", "Experimental": "Experimental", "Explore the cosmos": "Explorar el cosmos", "Export": "Exportar", "Export All Archived Chats": "Exportar tots els xats arxivats", "Export All Chats (All Users)": "Exportar tots els xats (Tots els usuaris)", "Export chat (.json)": "Exportar el xat (.json)", "Export Chats": "Exportar els xats", "Export Config to JSON File": "Exportar la configuració a un arxiu JSON", "Export Functions": "Exportar funcions", "Export Models": "Exportar els models", "Export Presets": "Exportar les configuracions", "Export Prompts": "Exportar les indicacions", "Export to CSV": "Exportar a CSV", "Export Tools": "Exportar les eines", "External Models": "Models externs", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "No s'ha pogut afegir l'arxiu.", "Failed to create API Key.": "No s'ha pogut crear la clau API.", "Failed to read clipboard contents": "No s'ha pogut llegir el contingut del porta-retalls", "Failed to save models configuration": "No s'ha pogut desar la configuració dels models", "Failed to update settings": "No s'han pogut actualitzar les preferències", "February": "<PERSON><PERSON>", "Feedback History": "<PERSON><PERSON><PERSON><PERSON>", "Feedbacks": "<PERSON><PERSON><PERSON>", "File": "<PERSON><PERSON><PERSON><PERSON>", "File added successfully.": "<PERSON>'ar<PERSON><PERSON> s'ha afegit correctament.", "File content updated successfully.": "El contingut de l'arxiu s'ha actualitzat correctament.", "File Mode": "Mode d'arxiu", "File not found.": "No s'ha trobat l'arxiu.", "File removed successfully.": "Ar<PERSON>u eliminat correctament.", "File size should not exceed {{maxSize}} MB.": "La mida del fitxer no ha de superar els {{maxSize}} MB.", "File uploaded successfully": "arxiu pujat satisfactòriament", "Files": "<PERSON><PERSON><PERSON><PERSON>", "Filter is now globally disabled": "El filtre ha estat desactivat globalment", "Filter is now globally enabled": "El filtre ha estat activat globalment", "Filters": "Filtres", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "S'ha detectat la suplantació d'identitat de l'empremta digital: no es poden utilitzar les inicials com a avatar. S'estableix la imatge de perfil predeterminada.", "Fluidly stream large external response chunks": "Transmetre amb fluïdesa grans trossos de resposta externa", "Focus chat input": "Estableix el focus a l'entrada del xat", "Folder deleted successfully": "Carpeta eliminada correctament", "Folder name cannot be empty": "El nom de la carpeta no pot ser buit", "Folder name cannot be empty.": "El nom de la carpeta no pot ser buit.", "Folder name updated successfully": "Nom de la carpeta actualitzat correctament", "Forge new paths": "Crea nous camins", "Form": "Formulari", "Format your variables using brackets like this:": "Formata les teves variables utilitzant claudàtors així:", "Frequency Penalty": "Penalització per freqüència", "Function": "<PERSON><PERSON><PERSON>", "Function created successfully": "La funció s'ha creat correctament", "Function deleted successfully": "La funció s'ha eliminat correctament", "Function Description": "Descripció de la funció", "Function ID": "ID de la funció", "Function is now globally disabled": "La funció ha estat desactivada globalment", "Function is now globally enabled": "La funció ha estat activada globalment", "Function Name": "Nom de la funció", "Function updated successfully": "La funció s'ha actualitzat correctament", "Functions": "Funcions", "Functions allow arbitrary code execution": "Les funcions permeten l'execució de codi arbitrari", "Functions allow arbitrary code execution.": "Les funcions permeten l'execució de codi arbitrari.", "Functions imported successfully": "Les funcions s'han importat correctament", "General": "General", "General Settings": "Preferències generals", "Generate Image": "Generar imatge", "Generating search query": "Generant consulta", "Get started": "<PERSON><PERSON><PERSON><PERSON>", "Get started with {{WEBUI_NAME}}": "Començar amb {{WEBUI_NAME}}", "Global": "Global", "Good Response": "<PERSON>a resposta", "Google Drive": "Google Drive", "Google PSE API Key": "Clau API PSE de Google", "Google PSE Engine Id": "Identificador del motor PSE de Google", "Group created successfully": "El grup s'ha creat correctament", "Group deleted successfully": "El grup s'ha eliminat correctament", "Group Description": "Descripció del grup", "Group Name": "Nom del grup", "Group updated successfully": "Grup actualitzat correctament", "Groups": "Grups", "GSA Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "h:mm a": "h:mm a", "Haptic Feedback": "<PERSON><PERSON><PERSON> h<PERSON>", "Harmful or offensive": "", "has no conversations.": "no té converses.", "Hello, {{name}}": "<PERSON><PERSON>, {{name}}", "Help": "<PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "Ajuda'ns a crear la millor taula de classificació de la comunitat compartint el teu historial de comentaris!", "Hex Color": "Color hexadecimal", "Hex Color - Leave empty for default color": "Color hexadecimal - Deixar buit per a color per defecte", "Hide": "<PERSON><PERSON>", "Host": "<PERSON><PERSON><PERSON>", "How can I help you today?": "Com et puc ajudar avui?", "How would you rate this response?": "Com avaluaries aquesta resposta?", "Hybrid Search": "Cerca híbrida", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Afirmo que he llegit i entenc les implicacions de la meva acció. Soc conscient dels riscos associats a l'execució de codi arbitrari i he verificat la fiabilitat de la font.", "ID": "ID", "Ignite curiosity": "Despertar la curiositat", "Image Compression": "Compressió d'imatges", "Image Generation (Experimental)": "<PERSON>rac<PERSON><PERSON> (Experimental)", "Image Generation Engine": "Motor de generació d'imatges", "Image Max Compression Size": "Mida màxima de la compressió d'imatges", "Image Settings": "Preferències d'imatges", "Images": "Imatges", "Import Chats": "Importar xats", "Import Config from JSON File": "Importar la configuració des d'un arxiu JSON", "Import Functions": "Importar funcions", "Import Models": "Importar models", "Import Presets": "Importar configuracions", "Import Prompts": "Importar indicacions", "Import Tools": "Importar eines", "Include": "<PERSON><PERSON><PERSON>", "Include `--api-auth` flag when running stable-diffusion-webui": "Inclou `--api-auth` quan executis stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Inclou `--api` quan executis stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "Influeix amb la rapidesa amb què l'algoritme respon als comentaris del text generat. Una taxa d'aprenentatge més baixa donarà lloc a ajustos més lents, mentre que una taxa d'aprenentatge més alta farà que l'algorisme sigui més sensible. (Per defecte: 0,1)", "Info": "Informació", "Input commands": "En<PERSON> comandes", "Install from Github URL": "Instal·lar des de l'URL de Github", "Instant Auto-Send After Voice Transcription": "Enviament automàtic després de la transcripció de veu", "Interface": "Interfície", "Invalid file format.": "Format d'arxiu no vàlid.", "Invalid Tag": "Etiqueta no vàlida", "is typing...": "", "January": "Gener", "Jina API Key": "<PERSON><PERSON> <PERSON>", "join our Discord for help.": "uneix-te al nostre Discord per obtenir ajuda.", "JSON": "JSON", "JSON Preview": "Vista prèvia del document JSON", "July": "<PERSON><PERSON>", "June": "<PERSON><PERSON>", "JWT Expiration": "Caducitat del JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "Clau API de Kagi Search", "Keep Alive": "Manté actiu", "Key": "<PERSON><PERSON>", "Keyboard shortcuts": "Dreceres de teclat", "Knowledge": "Coneixement", "Knowledge Access": "Accés al coneixement", "Knowledge created successfully.": "Coneixement creat correctament.", "Knowledge deleted successfully.": "Coneixement eliminat correctament.", "Knowledge reset successfully.": "Coneixement restablert correctament.", "Knowledge updated successfully": "Coneixement actualitzat correctament.", "Label": "Etiqueta", "Landing Page Mode": "Mode de la pàgina d'entrada", "Language": "Idioma", "Last Active": "Activitat recent", "Last Modified": "Modificació", "Last reply": "", "Latest users": "", "LDAP": "LDAP", "LDAP server updated": "Servidor LDAP actualitzat", "Leaderboard": "Tauler de classificació", "Leave empty for unlimited": "Deixar-ho buit per il·limitat", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "Deixar-ho buit per incloure tots els models del punt de connexió \"{{URL}}/api/tags\"", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "Deixar-ho buit per incloure tots els models del punt de connexió \"{{URL}}/models\"", "Leave empty to include all models or select specific models": "Deixa-ho en blanc per incloure tots els models o selecciona models específics", "Leave empty to use the default prompt, or enter a custom prompt": "Deixa-ho en blanc per utilitzar la indicació predeterminada o introdueix una indicació personalitzada", "Light": "<PERSON><PERSON>", "Listening...": "Escoltant...", "Local": "Local", "Local Models": "Models locals", "Lost": "<PERSON><PERSON><PERSON>", "LTR": "LTR", "Made by OpenWebUI Community": "Creat per la Comunitat OpenWebUI", "Make sure to enclose them with": "Assegura't d'envoltar-los amb", "Make sure to export a workflow.json file as API format from ComfyUI.": "Assegura't d'exportar un fitxer workflow.json com a format API des de ComfyUI.", "Manage": "Gestionar", "Manage Arena Models": "Gestionar els models de l'Arena", "Manage Ollama": "Gest<PERSON><PERSON>", "Manage Ollama API Connections": "Gestionar les connexions a l'API d'Ollama", "Manage OpenAI API Connections": "Gestionar les connexions a l'API d'OpenAI", "Manage Pipelines": "Gestionar les Pipelines", "March": "Març", "Max Tokens (num_predict)": "Nombre màxim de Tokens (num_predict)", "Max Upload Count": "Nombre màxim de càrregues", "Max Upload Size": "Mida màxima de càrrega", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Es poden descarregar un màxim de 3 models simultàniament. Si us plau, prova-ho més tard.", "May": "<PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "Les memòries accessibles pels models de llenguatge es mostraran aquí.", "Memory": "Memò<PERSON>", "Memory added successfully": "Memòria afegida correctament", "Memory cleared successfully": "Memòria eliminada correctament", "Memory deleted successfully": "Memòria eliminada correctament", "Memory updated successfully": "Memòria actualitzada correctament", "Merge Responses": "Fusionar les respostes", "Message rating should be enabled to use this feature": "La classificació dels missatges s'hauria d'activar per utilitzar aquesta funció", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Els missatges enviats després de crear el teu enllaç no es compartiran. Els usuaris amb l'URL podran veure el xat compartit.", "Min P": "<PERSON>", "Minimum Score": "Puntuació m<PERSON>", "Mirostat": "Mirostat", "Mirostat Eta": "Eta de Mirostat", "Mirostat Tau": "Tau de Mirostat", "MMMM DD, YYYY": "DD de MMMM, YYYY", "MMMM DD, YYYY HH:mm": "DD de MMMM, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "DD de MMMM, YYYY HH:mm:ss, A", "Model": "Model", "Model '{{modelName}}' has been successfully downloaded.": "El model '{{modelName}}' s'ha descarregat correctament.", "Model '{{modelTag}}' is already in queue for downloading.": "El model '{{modelTag}}' ja està en cua per ser descarregat.", "Model {{modelId}} not found": "No s'ha trobat el model {{modelId}}", "Model {{modelName}} is not vision capable": "El model {{modelName}} no és capaç de visió", "Model {{name}} is now {{status}}": "El model {{name}} ara és {{status}}", "Model accepts image inputs": "El model accepta entrades d'imatge", "Model created successfully!": "Model creat correctament", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "S'ha detectat el camí del sistema de fitxers del model. És necessari un nom curt del model per actualitzar, no es pot continuar.", "Model Filtering": "Filtrat de models", "Model ID": "Identificador del model", "Model IDs": "Identificadors del model", "Model Name": "Nom del model", "Model not selected": "Model no seleccionat", "Model Params": "Paràmetres del model", "Model Permissions": "Permisos dels models", "Model updated successfully": "Model actualitzat correctament", "Modelfile Content": "Contingut del Modelfile", "Models": "Models", "Models Access": "Accés als models", "Models configuration saved successfully": "La configuració dels models s'ha desat correctament", "Mojeek Search API Key": "Clau API de Mojeek Search", "more": "més", "More": "Més", "Name": "Nom", "Name your knowledge base": "Anomena la teva base de coneixement", "New Chat": "Nou xat", "New folder": "Nova carpeta", "New Password": "Nova contrasenya", "new-channel": "nou-canal", "No content found": "No s'ha trobat contingut", "No content to speak": "No hi ha contingut per parlar", "No distance available": "No hi ha distància disponible", "No feedbacks found": "No s'han trobat comentaris", "No file selected": "No s'ha escollit cap fitxer", "No files found.": "No s'han trobat arxius.", "No groups with access, add a group to grant access": "No hi ha cap grup amb accés, afegeix un grup per concedir accés", "No HTML, CSS, or JavaScript content found.": "No s'ha trobat contingut HTML, CSS o JavaScript.", "No knowledge found": "No s'ha trobat Coneixement", "No model IDs": "No hi ha IDs de model", "No models found": "No s'han trobat models", "No models selected": "No s'ha seleccionat cap model", "No results found": "No s'han trobat resultats", "No search query generated": "No s'ha generat cap consulta", "No source available": "Sense font disponible", "No users were found.": "No s'han trobat usuaris", "No valves to update": "No hi ha cap Valve per actualitzar", "None": "Cap", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Nota: Si s'estableix una puntuació mínima, la cerca només retornarà documents amb una puntuació major o igual a la puntuació mínima.", "Notes": "Notes", "Notification Sound": "So de la notificació", "Notification Webhook": "Webhook de la notificació", "Notifications": "Notificacions", "November": "Novembre", "num_gpu (Ollama)": "num_gpu (Ollama)", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "ID OAuth", "October": "Octubre", "Off": "Desactivat", "Okay, Let's Go!": "D'acord, som-hi!", "OLED Dark": "OLED Fosc", "Ollama": "Ollama", "Ollama API": "API d'Ollama", "Ollama API disabled": "API d'Ollama desactivada", "Ollama API settings updated": "La configuració de l'API d'Ollama s'ha actualitzat", "Ollama Version": "Versió <PERSON>", "On": "Activat", "Only alphanumeric characters and hyphens are allowed": "Només es permeten caràcters alfanumèrics i guions", "Only alphanumeric characters and hyphens are allowed in the command string.": "Només es permeten caràcters alfanumèrics i guions en la comanda.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Només es poden editar col·leccions, crea una nova base de coneixement per editar/afegir documents.", "Only select users and groups with permission can access": "Només hi poden accedir usuaris i grups seleccionats amb permís", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Ui! Sembla que l'URL no és vàlida. Si us plau, revisa-la i torna-ho a provar.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Ui! Encara hi ha fitxers pujant-se. Si us plau, espera que finalitzi la càrrega.", "Oops! There was an error in the previous response.": "Ui! Hi ha hagut un error a la resposta anterior.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Ui! Estàs utilitzant un mètode no suportat (només frontend). Si us plau, serveix la WebUI des del backend.", "Open in full screen": "Obrir en pantalla complerta", "Open new chat": "Obre un xat nou", "Open WebUI uses faster-whisper internally.": "Open WebUI utilitza faster-whisper internament.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI utilitza incrustacions de SpeechT5 i CMU Arctic.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "La versió d'Open WebUI (v{{OPEN_WEBUI_VERSION}}) és inferior a la versió requerida (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "API d'OpenAI", "OpenAI API Config": "Configuració de l'API d'OpenAI", "OpenAI API Key is required.": "Es requereix la clau API d'OpenAI.", "OpenAI API settings updated": "Configuració de l'API d'OpenAI actualitzada", "OpenAI URL/Key required.": "URL/Clau d'OpenAI requerides.", "or": "o", "Organize your users": "<PERSON><PERSON><PERSON> els teus usuaris", "OUTPUT": "SORTIDA", "Output format": "Format de sortida", "Overview": "Vista general", "page": "pàgina", "Password": "Contrasenya", "Paste Large Text as File": "Enganxa un text llarg com a fitxer", "PDF document (.pdf)": "Document PDF (.pdf)", "PDF Extract Images (OCR)": "Extreu imatges del PDF (OCR)", "pending": "pendent", "Permission denied when accessing media devices": "Permís denegat en accedir a dispositius multimèdia", "Permission denied when accessing microphone": "Permís denegat en accedir al micròfon", "Permission denied when accessing microphone: {{error}}": "Permís denegat en accedir al micròfon: {{error}}", "Permissions": "<PERSON><PERSON><PERSON>", "Personalization": "Personalitz<PERSON><PERSON><PERSON>", "Pin": "Fixar", "Pinned": "Fixat", "Pioneer insights": "Perspectives pioneres", "Pipeline deleted successfully": "Pipeline eliminada correctament", "Pipeline downloaded successfully": "Pipeline descarregada correctament", "Pipelines": "Pipelines", "Pipelines Not Detected": "No s'ha detectat Pipelines", "Pipelines Valves": "Vàlvules de les Pipelines", "Plain text (.txt)": "Text pla (.txt)", "Playground": "Zona de jocs", "Please carefully review the following warnings:": "Si us plau, revisa els següents avisos amb cura:", "Please enter a prompt": "Si us plau, entra una indicació", "Please fill in all fields.": "Emplena tots els camps, si us plau.", "Please select a model first.": "Si us plau, selecciona un model primer", "Port": "Port", "Prefix ID": "Identificador del prefix", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "L'identificador de prefix s'utilitza per evitar conflictes amb altres connexions afegint un prefix als ID de model; deixa'l en blanc per desactivar-lo.", "Previous 30 days": "30 dies anteriors", "Previous 7 days": "7 dies anteriors", "Profile Image": "Imatge de perfil", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Indicació (p.ex. Digues-me quelcom divertit sobre l'Imperi Romà)", "Prompt Content": "Contingut de la indicació", "Prompt created successfully": "Indicació creada correctament", "Prompt suggestions": "Suggeriments d'indicacions", "Prompt updated successfully": "Indicació actualitzada correctament", "Prompts": "Indicacions", "Prompts Access": "Accés a les indicacions", "Provide any specific details": "", "Proxy URL": "URL del proxy", "Pull \"{{searchValue}}\" from Ollama.com": "Obtenir \"{{searchValue}}\" de Ollama.com", "Pull a model from Ollama.com": "Obtenir un model d'Ollama.com", "Query Generation Prompt": "Indicació per a generació de consulta", "Query Params": "Paràmetres de consulta", "RAG Template": "Plantilla RAG", "Rating": "Valoració", "Re-rank models by topic similarity": "Reclassificar els models per similitud de temes", "Read Aloud": "Llegir en veu alta", "Record voice": "Enregistrar la veu", "Redirecting you to OpenWebUI Community": "Redirigint-te a la comunitat OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "Redueix la probabilitat de generar ximpleries. Un valor més alt (p. ex. 100) donarà respostes més diverses, mentre que un valor més baix (p. ex. 10) serà més conservador. (<PERSON> defecte: 40)", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Fes referència a tu mateix com a \"Usuari\" (p. ex., \"L'usuari està aprenent espanyol\")", "References from": "Referències de", "Refresh Token Expiration": "", "Regenerate": "<PERSON><PERSON><PERSON>", "Release Notes": "Notes de la versió", "Relevance": "Rellevància", "Remove": "Eliminar", "Remove Model": "Eliminar el model", "Rename": "Canviar el nom", "Reorder Models": "Reordenar els models", "Repeat Last N": "Repeteix els darrers N", "Reply in Thread": "", "Request Mode": "Mode de sol·licitud", "Reranking Model": "Model de reavaluació", "Reranking model disabled": "Model de reavaluació desactivat", "Reranking model set to \"{{reranking_model}}\"": "Model de reavaluació establert a \"{{reranking_model}}\"", "Reset": "Restableix", "Reset All Models": "Restablir tots els models", "Reset Upload Directory": "Restableix el directori de pujades", "Reset Vector Storage/Knowledge": "Restableix el Repositori de vectors/Coneixement", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Les notifications de resposta no es poden activar perquè els permisos del lloc web han estat rebutjats. Comprova les preferències del navegador per donar l'accés necessari.", "Response splitting": "Divisió de la resposta", "Result": "Resultat", "Retrieval Query Generation": "Generació de consultes Retrieval", "Rich Text Input for Chat": "Entrada de text ric per al xat", "RK": "RK", "Role": "Rol", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "Albada Rosé Pine", "RTL": "RTL", "Run": "Executar", "Running": "S'està executant", "Save": "Desar", "Save & Create": "Desar i crear", "Save & Update": "Desar i actualitzar", "Save As Copy": "Desar com a còpia", "Save Tag": "Desar l'etiqueta", "Saved": "Desat", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Desar els registres de xat directament a l'emmagatzematge del teu navegador ja no està suportat. Si us plau, descarregr i elimina els registres de xat fent clic al botó de sota. No et preocupis, pots tornar a importar fàcilment els teus registres de xat al backend a través de", "Scroll to bottom when switching between branches": "Desplaçar a la part inferior quan es canviï de branca", "Search": "Cercar", "Search a model": "Cercar un model", "Search Base": "Base de cerca", "Search Chats": "Cercar xats", "Search Collection": "Cercar col·leccions", "Search Filters": "Filtres de cerca", "search for tags": "cercar etiquetes", "Search Functions": "Cercar funcions", "Search Knowledge": "Cercar coneixement", "Search Models": "Cercar models", "Search options": "Opcions de cerca", "Search Prompts": "Cercar indicacions", "Search Result Count": "Recompte de resultats de cerca", "Search Tools": "Cercar eines", "Search users": "", "SearchApi API Key": "Clau API de SearchApi", "SearchApi Engine": "Motor de SearchApi", "Searched {{count}} sites": "S'han cercat {{count}} pàgines", "Searching \"{{searchQuery}}\"": "Cercant \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Cercant \"{{searchQuery}}\" al coneixement", "Searxng Query URL": "URL de consulta de Searxng", "See readme.md for instructions": "Consulta l'arxiu readme.md per obtenir instruccions", "See what's new": "Veure què hi ha de nou", "Seed": "<PERSON><PERSON><PERSON>", "Select a base model": "Seleccionar un model base", "Select a engine": "Seleccionar un motor", "Select a function": "Seleccionar una funció", "Select a group": "Seleccionar un grup", "Select a model": "Seleccionar un model", "Select a pipeline": "Seleccionar una Pipeline", "Select a pipeline url": "Seleccionar l'URL d'una Pipeline", "Select a tool": "Seleccionar una eina", "Select Engine": "Seleccionar el motor", "Select Knowledge": "Seleccionar cone<PERSON>ement", "Select model": "Seleccionar un model", "Select only one model to call": "Seleccionar només un model per trucar", "Selected model(s) do not support image inputs": "El(s) model(s) seleccionats no admeten l'entrada d'imatges", "Semantic distance to query": "Distància semàntica a la pregunta", "Send": "Enviar", "Send a message": "", "Send a Message": "Enviar un missatge", "Send message": "<PERSON><PERSON>r missatge", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Envia `stream_options: { include_usage: true }` a la sol·licitud.\nEls proveïdors compatibles retornaran la informació d'ús del token a la resposta quan s'estableixi.", "September": "Setembre", "Serper API Key": "Clau <PERSON>", "Serply API Key": "Clau <PERSON> de Serply", "Serpstack API Key": "Clau <PERSON>pstack", "Server connection verified": "Connexió al servidor verificada", "Set as default": "Establir com a predeterminat", "Set CFG Scale": "Establir l'escala CFG", "Set Default Model": "Establir el model predeterminat", "Set embedding model": "Establir el model d'incrustació", "Set embedding model (e.g. {{model}})": "<PERSON><PERSON><PERSON><PERSON><PERSON> el model d'incrustació (p.ex. {{model}})", "Set Image Size": "Establir la mida de la image", "Set reranking model (e.g. {{model}})": "Establir el model de reavaluació (p.ex. {{model}})", "Set Sampler": "Establir el mostrejador", "Set Scheduler": "Establir el programador", "Set Steps": "Establir el nombre de passos", "Set Task Model": "Establir el model de tasca", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Establir el nombre de dispositius GPU utilitzats per al càlcul. Aquesta opció controla quants dispositius GPU (si estan disponibles) s'utilitzen per processar les sol·licituds entrants. Augmentar aquest valor pot millorar significativament el rendiment dels models optimitzats per a l'acceleració de la GPU, però també pot consumir més energia i recursos de GPU.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Establir el nombre de fils de treball utilitzats per al càlcul. Aquesta opció controla quants fils s'utilitzen per processar les sol·licituds entrants simultàniament. Augmentar aquest valor pot millorar el rendiment amb càrregues de treball de concurrència elevada, però també pot consumir més recursos de CPU.", "Set Voice": "Establir la veu", "Set whisper model": "Establir el model whisper", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "Establir fins a quin punt el model mira enrere per evitar la repetició. (Per defecte: 64, 0 = desactivat, -1 = num_ctx)", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "Establir amb quina força penalitzar les repeticions. Un valor més alt (p. ex., 1,5) penalitzarà les repeticions amb més força, mentre que un valor més baix (p. ex., 0,9) serà més indulgent. (<PERSON> defecte: 1.1)", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "Establir la llavor del nombre aleatori que s'utilitzarà per a la generació. Establir-ho a un número específic farà que el model generi el mateix text per a la mateixa sol·licitud. (Per defecte: aleatori)", "Sets the size of the context window used to generate the next token. (Default: 2048)": "Estableix la mida de la finestra de context utilitzada per generar el següent token. (Per defecte: 2048)", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Establir les seqüències d'aturada a utilitzar. Quan es trobi aquest patró, el LLM deixarà de generar text. Es poden establir diversos patrons de parada especificant diversos paràmetres de parada separats en un fitxer model.", "Settings": "Preferències", "Settings saved successfully!": "Les preferències s'han desat correctament", "Share": "Compartir", "Share Chat": "Compartir el xat", "Share to OpenWebUI Community": "Compartir amb la comunitat OpenWebUI", "Show": "Mostrar", "Show \"What's New\" modal on login": "<PERSON><PERSON><PERSON> <PERSON><PERSON>uè hi ha de nou' a l'entrada", "Show Admin Details in Account Pending Overlay": "Mostrar els detalls de l'administrador a la superposició del compte pendent", "Show shortcuts": "<PERSON>rar dreceres", "Show your support!": "Mostra el teu suport!", "Sign in": "<PERSON><PERSON><PERSON>", "Sign in to {{WEBUI_NAME}}": "Iniciar <PERSON><PERSON><PERSON> a {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Iniciar se<PERSON><PERSON> a {{WEBUI_NAME}} amb LDAP", "Sign Out": "<PERSON><PERSON>", "Sign up": "Registrar-se", "Sign up to {{WEBUI_NAME}}": "Registrar-se a {{WEBUI_NAME}}", "Signing in to {{WEBUI_NAME}}": "Iniciant <PERSON><PERSON><PERSON> a {{WEBUI_NAME}}", "sk-1234": "sk-1234", "Source": "Font", "Speech Playback Speed": "Velocitat de la parla", "Speech recognition error: {{error}}": "Error de reconeixement de veu: {{error}}", "Speech-to-Text Engine": "Motor de veu a text", "Stop": "<PERSON><PERSON>", "Stop Sequence": "Atura la seqüència", "Stream Chat Response": "Fer streaming de la resposta del xat", "STT Model": "Model SST", "STT Settings": "Preferències de STT", "Success": "<PERSON><PERSON><PERSON>", "Successfully updated.": "Actualitzat correctament.", "Suggested prompts to get you started": "", "Support": "<PERSON><PERSON>", "Support this plugin:": "Dona suport a aquest complement:", "Sync directory": "Sincronitzar directori", "System": "Sistema", "System Instructions": "Instruccions de sistema", "System Prompt": "Indicació del Sistema", "Tags Generation": "Generació d'etiquetes", "Tags Generation Prompt": "Indicació per a la generació d'etiquetes", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "El mostreig sense cua s'utilitza per reduir l'impacte de tokens menys probables de la sortida. Un valor més alt (p. ex., 2,0) reduirà més l'impacte, mentre que un valor d'1,0 desactiva aquesta configuració. (per defecte: 1)", "Tap to interrupt": "Prem per interrompre", "Tavily API Key": "<PERSON><PERSON> <PERSON>", "Temperature": "Temperatura", "Template": "Plantilla", "Temporary Chat": "Xat temporal", "Text Splitter": "Separador de text", "Text-to-Speech Engine": "Motor de text a veu", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Gràcies pel teu comentari!", "The Application Account DN you bind with for search": "El DN del compte d'aplicació per realitzar la cerca", "The base to search for users": "La base per cercar usuaris", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "La mida del lot determina quantes sol·licituds de text es processen alhora. Una mida de lot més gran pot augmentar el rendiment i la velocitat del model, però també requereix més memòria. (Per defecte: 512)", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Els desenvolupadors d'aquest complement són voluntaris apassionats de la comunitat. Si trobeu útil aquest complement, considereu contribuir al seu desenvolupament.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "La classificació d'avaluació es basa en el sistema de qualificació Elo i s'actualitza en temps real.", "The LDAP attribute that maps to the username that users use to sign in.": "L'atribut LDAP que mapeja el nom d'usuari amb l'usuari que vol iniciar sessió", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "La classificació està actualment en versió beta i és possible que s'ajustin els càlculs de la puntuació a mesura que es perfeccioni l'algorisme.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "La mida màxima del fitxer en MB. Si la mida del fitxer supera aquest límit, el fitxer no es carregarà.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "El nombre màxim de fitxers que es poden utilitzar alhora al xat. Si el nombre de fitxers supera aquest límit, els fitxers no es penjaran.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "El valor de puntuació hauria de ser entre 0.0 (0%) i 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "La temperatura del model. Augmentar la temperatura farà que el model respongui de manera més creativa. (Per defecte: 0,8)", "Theme": "<PERSON><PERSON>", "Thinking...": "Pensant...", "This action cannot be undone. Do you wish to continue?": "Aquesta acció no es pot desfer. Vols continuar?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Això assegura que les teves converses valuoses queden desades de manera segura a la teva base de dades. Gràcies!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Aquesta és una funció experimental, és possible que no funcioni com s'espera i està subjecta a canvis en qualsevol moment.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "Aquesta opció controla quants tokens es conserven en actualitzar el context. Per exemple, si s'estableix en 2, es conservaran els darrers 2 tokens del context de conversa. Preservar el context pot ajudar a mantenir la continuïtat d'una conversa, però pot reduir la capacitat de respondre a nous temes. (Per defecte: 24)", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "Aquesta opció estableix el nombre màxim de tokens que el model pot generar en la seva resposta. Augmentar aquest límit permet que el model proporcioni respostes més llargues, però també pot augmentar la probabilitat que es generi contingut poc útil o irrellevant. (Per defecte: 128)", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Aquesta opció eliminarà tots els fitxers existents de la col·lecció i els substituirà per fitxers recentment penjats.", "This response was generated by \"{{model}}\"": "Aquesta resposta l'ha generat el model \"{{model}}\"", "This will delete": "<PERSON><PERSON><PERSON> eliminar<PERSON>", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON><PERSON><PERSON> eliminarà <strong>{{NAME}}</strong> i <strong>tots els continguts</strong>.", "This will delete all models including custom models": "<PERSON><PERSON>ò eliminarà tots els models incloent els personalitzats", "This will delete all models including custom models and cannot be undone.": "Ai<PERSON>ò eliminarà tots els models incloent els personalitzats i no es pot desfer", "This will reset the knowledge base and sync all files. Do you wish to continue?": "<PERSON><PERSON>ò restablirà la base de coneixement i sincronitzarà tots els fitxers. Vols continuar?", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "La URL del servidor Tika és obligatòria.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Consell: Actualitza les diverses variables consecutivament prement la tecla de tabulació en l'entrada del xat després de cada reemplaçament.", "Title": "Títol", "Title (e.g. Tell me a fun fact)": "<PERSON><PERSON><PERSON><PERSON> (p. ex. Digues-me quelcom divertit)", "Title Auto-Generation": "Generació automàtica de títol", "Title cannot be an empty string.": "El títol no pot ser una cadena buida.", "Title Generation Prompt": "Indicació de generació de títol", "TLS": "TLS", "To access the available model names for downloading,": "Per accedir als noms dels models disponibles per descarregar,", "To access the GGUF models available for downloading,": "Per accedir als models GGUF disponibles per descarregar,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Per accedir a la WebUI, poseu-vos en contacte amb l'administrador. Els administradors poden gestionar els estats dels usuaris des del tauler d'administració.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Per adjuntar la base de coneixement aquí, afegiu-la primer a l'espai de treball \"Coneixement\".", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Per protegir la privadesa, només es comparteixen puntuacions, identificadors de models, etiquetes i metadades dels comentaris; els registres de xat romanen privats i no s'inclouen.", "To select actions here, add them to the \"Functions\" workspace first.": "Per seleccionar accions aquí, afegeix-les primer a l'espai de treball \"Funcions\".", "To select filters here, add them to the \"Functions\" workspace first.": "Per seleccionar filtres aquí, afegeix-los primer a l'espai de treball \"Funcions\".", "To select toolkits here, add them to the \"Tools\" workspace first.": "Per seleccionar kits d'eines aquí, afegeix-los primer a l'espai de treball \"Eines\".", "Toast notifications for new updates": "Notificacions Toast de noves actualitzacions", "Today": "<PERSON><PERSON><PERSON>", "Toggle settings": "Alterna preferències", "Toggle sidebar": "Alterna la barra lateral", "Token": "Token", "Tokens To Keep On Context Refresh (num_keep)": "Tokens a mantenir en l'actualització del context (num_keep)", "Tool created successfully": "<PERSON>a creada correctament", "Tool deleted successfully": "Eina eliminada correctament", "Tool Description": "Descripció de l'eina", "Tool ID": "ID de l'eina", "Tool imported successfully": "Eina importada correctament", "Tool Name": "Nom de l'eina", "Tool updated successfully": "<PERSON><PERSON> correctament", "Tools": "<PERSON><PERSON>", "Tools Access": "Accés a les eines", "Tools are a function calling system with arbitrary code execution": "Les eines són un sistema de crida a funcions amb execució de codi arbitrari", "Tools have a function calling system that allows arbitrary code execution": "Les eines disposen d'un sistema de crida a funcions que permet execució de codi arbitrari", "Tools have a function calling system that allows arbitrary code execution.": "Les eines disposen d'un sistema de crida a funcions que permet execució de codi arbitrari.", "Top K": "Top K", "Top P": "Top P", "Transformers": "Transformadors", "Trouble accessing Ollama?": "Problemes en accedir a Ollama?", "TTS Model": "Model TTS", "TTS Settings": "Preferències de TTS", "TTS Voice": "Veu TTS", "Type": "<PERSON><PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "Escriu l'URL de Resolució (Descàrrega) de Hugging Face", "Uh-oh! There was an issue with the response.": "Vaja! Hi ha hagut una incidència amb la resposta.", "UI": "UI", "Unarchive All": "Desar<PERSON><PERSON> tot", "Unarchive All Archived Chats": "Desarxivar tots els xats arxivats", "Unarchive Chat": "Desarxivar xat", "Unlock mysteries": "Desbloqueja els misteris", "Unpin": "Alliberar", "Unravel secrets": "Descobreix els secrets", "Untagged": "Sense etiquetes", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update and Copy Link": "Actualitzar i copiar l'enllaç", "Update for the latest features and improvements.": "Actualitza per a les darreres característiques i millores.", "Update password": "Actualitzar la contrasenya", "Updated": "Actualitzat", "Updated at": "Actualitzat el", "Updated At": "Actualitzat el", "Upload": "<PERSON><PERSON><PERSON>", "Upload a GGUF model": "Pujar un model GGUF", "Upload directory": "<PERSON><PERSON>jar directori", "Upload files": "<PERSON><PERSON><PERSON> fitxe<PERSON>", "Upload Files": "<PERSON><PERSON><PERSON> fitxe<PERSON>", "Upload Pipeline": "<PERSON><PERSON>jar una Pipeline", "Upload Progress": "Progrés de c<PERSON>rrega", "URL": "URL", "URL Mode": "Mode URL", "Use '#' in the prompt input to load and include your knowledge.": "Utilitza '#' a l'entrada de la indicació per carregar i incloure els teus coneixements.", "Use Gravatar": "<PERSON><PERSON><PERSON><PERSON>", "Use groups to group your users and assign permissions.": "Utilitza grups per agrupar els usuaris i assignar permisos.", "Use Initials": "Utilitzar inicials", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "usuari", "User": "<PERSON><PERSON><PERSON>", "User location successfully retrieved.": "Ubicació de l'usuari obtinguda correctament", "Username": "Nom d'usuari", "Users": "<PERSON><PERSON><PERSON>", "Using the default arena model with all models. Click the plus button to add custom models.": "S'utilitza el model d'Arena predeterminat amb tots els models. Clica el botó més per afegir models personalitzats.", "Utilize": "<PERSON><PERSON><PERSON><PERSON>", "Valid time units:": "Unitats de temps vàlides:", "Valves": "Valves", "Valves updated": "Valves <PERSON>", "Valves updated successfully": "Valves actualitat correctament", "variable": "variable", "variable to have them replaced with clipboard content.": "variable per tenir-les reemplaçades amb el contingut del porta-retalls.", "Version": "<PERSON><PERSON><PERSON><PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "Versió {{selectedVersion}} de {{totalVersions}}", "Very bad": "", "View Replies": "", "Visibility": "Visibilitat", "Voice": "Veu", "Voice Input": "Entrada de veu", "Warning": "<PERSON><PERSON><PERSON>", "Warning:": "Avís:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Avís: <PERSON>bil<PERSON>r a<PERSON> permetrà als usuaris penjar codi arbitrari al servidor.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Avís: <PERSON> s'actualitza o es canvia el model d'incrustació, s'hauran de tornar a importar tots els documents.", "Web": "Web", "Web API": "Web API", "Web Loader Settings": "Preferències del carregador web", "Web Search": "Cerca la web", "Web Search Engine": "Motor de cerca de la web", "Web Search Query Generation": "Generació de consultes per a la cerca de la web", "Webhook URL": "URL del webhook", "WebUI Settings": "Preferències de WebUI", "WebUI URL": "URL de WebUI", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI farà peticions a \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI farà peticions a \"{{url}}/chat/completions\"", "Welcome, {{name}}!": "", "What are you trying to achieve?": "Què intentes aconseguir?", "What are you working on?": "En què estàs treballant?", "What didn't you like about this response?": "", "What’s New in": "Què hi ha de nou a", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "Quan està activat, el model respondrà a cada missatge de xat en temps real, generant una resposta tan bon punt l'usuari envia un missatge. Aquest mode és útil per a aplicacions de xat en directe, però pot afectar el rendiment en maquinari més lent.", "wherever you are": "allà on estiguis", "Whisper (Local)": "<PERSON><PERSON><PERSON> (local)", "Widescreen Mode": "Mode de pantalla ampla", "Won": "Ha guanyat", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "Funciona juntament amb top-k. Un valor més alt (p. ex., 0,95) donarà lloc a un text més divers, mentre que un valor més baix (p. ex., 0,5) generarà un text més concentrat i conservador. (Per defecte: 0,9)", "Workspace": "Espai de treball", "Workspace Permissions": "Permisos de l'espai de treball", "Write a prompt suggestion (e.g. Who are you?)": "Escriu una suggerència d'indicació (p. ex. Qui ets?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Escriu un resum en 50 paraules que resumeixi [tema o paraula clau].", "Write something...": "Escriu quelcom...", "Write your model template content here": "Introdueix el contingut de la plantilla del teu model aquí", "Yesterday": "<PERSON><PERSON>", "You": "Tu", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Només pots xatejar amb un màxim de {{maxCount}} fitxers alhora.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Pots personalitzar les teves interaccions amb els models de llenguatge afegint memòries mitjançant el botó 'Gestiona' que hi ha a continuació, fent-les més útils i adaptades a tu.", "You cannot upload an empty file.": "No es pot pujar un ariux buit.", "You have no archived conversations.": "No tens converses arxivades.", "You have shared this chat": "Has compartit aquest xat", "You're a helpful assistant.": "Ets un assistent útil.", "Your account status is currently pending activation.": "El compte està actualment pendent d'activació", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Tota la teva contribució anirà directament al desenvolupador del complement; Open WebUI no se'n queda cap percentatge. Tanmateix, la plataforma de finançament escollida pot tenir les seves pròpies comissions.", "Youtube": "Youtube", "Youtube Loader Settings": "Preferències del carregador de Youtube"}