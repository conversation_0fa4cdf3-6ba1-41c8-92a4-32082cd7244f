{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' eller '-1' for ingen udløb", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(f.eks. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(f.eks. `sh webui.sh --api`)", "(latest)": "(seneste)", "{{ models }}": "{{ modeller }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}}s chats", "{{webUIName}} Backend Required": "{{webUIName}} Backend kræves", "*Prompt node ID(s) are required for image generation": "*Prompt node ID(s) er påkrævet for at kunne generere billeder", "A new version (v{{LATEST_VERSION}}) is now available.": "En ny version (v{{LATEST_VERSION}}) er nu tilgængelig.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "En 'task model' bliver brugt til at opgaver såsom at generere overskrifter til chats eller internetsøgninger", "a user": "en bruger", "About": "Information", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "Profil", "Account Activation Pending": "Aktivering af profil afventer", "Actions": "<PERSON><PERSON>", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "Aktive brugere", "Add": "Tilføj", "Add a model ID": "", "Add a short description about what this model does": "En kort beskrivelse af hvad denne model gør", "Add a tag": "Tilføj et tag", "Add Arena Model": "", "Add Connection": "", "Add Content": "<PERSON><PERSON><PERSON><PERSON><PERSON> indhold", "Add content here": "<PERSON><PERSON><PERSON><PERSON><PERSON> in<PERSON>old her", "Add custom prompt": "Til<PERSON><PERSON><PERSON> en special-prompt", "Add Files": "<PERSON><PERSON><PERSON><PERSON><PERSON> filer", "Add Group": "", "Add Memory": "<PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON>", "Add Model": "Tilføj model", "Add Reaction": "", "Add Tag": "<PERSON><PERSON><PERSON><PERSON><PERSON> tag", "Add Tags": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "Add text content": "Tilføj tekst", "Add User": "<PERSON><PERSON><PERSON><PERSON><PERSON> bruger", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Ændringer af disse indstillinger har konsekvenser for alle brugere.", "admin": "administrator", "Admin": "Administrator", "Admin Panel": "Administrationspanel", "Admin Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Administratorer har adgang til alle værktøjer altid; brugere skal tilføjes værktøjer pr. model i hvert workspace.", "Advanced Parameters": "<PERSON><PERSON><PERSON>", "Advanced Params": "<PERSON><PERSON><PERSON>", "All Documents": "Alle dokumenter", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "<PERSON><PERSON> sletning af chats", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "Tillad ikke-lokale stemmer", "Allow Temporary Chat": "<PERSON><PERSON> midlertidig chat", "Allow User Location": "Tillad bruger-lokation", "Allow Voice Interruption in Call": "Tillad afbrydelser i stemme i opkald", "Allowed Endpoints": "", "Already have an account?": "Har du allerede en profil?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "en assistent", "and": "og", "and {{COUNT}} more": "", "and create a new shared link.": "og lav et nyt link til deling", "API Base URL": "API Base URL", "API Key": "API nøgle", "API Key created.": "API nøgle lavet", "API Key Endpoint Restrictions": "", "API keys": "API nøgler", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "april", "Archive": "Arkiv", "Archive All Chats": "<PERSON><PERSON> alle chats", "Archived Chats": "Arkiverede chats", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "<PERSON>r du sikker?", "Arena Models": "", "Artifacts": "Artifakter", "Ask a question": "Stil et spørgsmål", "Assistant": "", "Attach file": "Vedhæft fil", "Attribute for Username": "", "Audio": "Lyd", "August": "august", "Authenticate": "", "Auto-Copy Response to Clipboard": "Automatisk kopiering af svar til udklipsholder", "Auto-playback response": "Automatisk afspil svar", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Api Auth String", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 Base URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 Base URL er påkrævet.", "Available list": "Tilgængeli<PERSON> lister", "available!": "tilgængelig!", "Azure AI Speech": "Azure AI Speech", "Azure Region": "Azure Region", "Back": "Tilbage", "Bad": "", "Bad Response": "Problem i response", "Banners": "<PERSON><PERSON>", "Base Model (From)": "Base Model (Fra)", "Batch Size (num_batch)": "<PERSON><PERSON> størrel<PERSON> (num_batch)", "before": "<PERSON>ør", "Beta": "", "BETA": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Brave Search API nøgle", "By {{name}}": "", "Bypass SSL verification for Websites": "Forbigå SSL verifikation på websider", "Call": "Opkald", "Call feature is not supported when using Web STT engine": "Opkaldsfunktion er ikke understøttet for Web STT engine", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Capabilities": "<PERSON><PERSON><PERSON>", "Capture": "", "Certificate Path": "", "Change Password": "Skift password", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "Cha<PERSON>", "Chat Background Image": "<PERSON><PERSON>", "Chat Bubble UI": "Chat Bubble UI", "Chat Controls": "<PERSON><PERSON><PERSON>", "Chat direction": "Chat retning", "Chat Overview": "Chat overblik", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "Chats", "Check Again": "Tjek igen", "Check for updates": "<PERSON><PERSON><PERSON> efter op<PERSON>", "Checking for updates...": "<PERSON><PERSON><PERSON> efter op<PERSON>", "Choose a model before saving...": "Vælg en model før du gemmer", "Chunk Overlap": "Chunk overlap", "Chunk Params": "Chunk parametre", "Chunk Size": "Chunk størrelse", "Ciphers": "", "Citation": "Citat", "Clear memory": "Slet hukommelse", "click here": "", "Click here for filter guides.": "", "Click here for help.": "Klik her for hjælp", "Click here to": "Klik her for at", "Click here to download user import template file.": "<PERSON>lik her for at downloade bruger import template fil.", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "Klik her for at vælge", "Click here to select a csv file.": "Klik her for at vælge en csv fil", "Click here to select a py file.": "Klik her for at vælge en py fil", "Click here to upload a workflow.json file.": "Klik her for at uploade en workflow.json fil", "click here.": "klik her.", "Click on the user role button to change a user's role.": "<PERSON><PERSON> på bruger ikonet for at ændre brugerens rolle.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Skriveadgang til udklipsholderen ikke tilladt. Tjek venligst indstillingerne i din browser for at give adgang.", "Clone": "Klon", "Close": "Luk", "Code execution": "", "Code formatted successfully": "Kode formateret korrekt", "Collection": "<PERSON><PERSON>", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI Base URL", "ComfyUI Base URL is required.": "ComfyUI Base URL er påkrævet.", "ComfyUI Workflow": "ComfyUI Workflow", "ComfyUI Workflow Nodes": "ComfyUI Workflow Nodes", "Command": "Kommando", "Completions": "", "Concurrent Requests": "Concurrent requests", "Configure": "", "Configure Models": "", "Confirm": "Bekræft", "Confirm Password": "Bekræft password", "Confirm your action": "Bekræft din handling", "Confirm your new password": "", "Connections": "For<PERSON><PERSON><PERSON>", "Contact Admin for WebUI Access": "Kontakt din administrator for adgang til WebUI", "Content": "Indhold", "Content Extraction": "Udtræk af indhold", "Context Length": "Kontekst længde", "Continue Response": "Fortsæt svar", "Continue with {{provider}}": "Fortsæt med {{provider}}", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Kontroller hvordan beskedens tekst bliver splittet til TTS requests. 'Punctuation' (tegnsætning) splitter i sætninger, 'paragraphs' splitter i paragraffer, og 'none' beholder beskeden som en samlet streng.", "Controls": "<PERSON><PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "<PERSON><PERSON><PERSON>", "Copied shared chat URL to clipboard!": "Link til deling kopieret til udklipsholder", "Copied to clipboard": "<PERSON><PERSON><PERSON> til udklipsholder", "Copy": "<PERSON><PERSON><PERSON>", "Copy last code block": "<PERSON><PERSON><PERSON> seneste kode", "Copy last response": "<PERSON><PERSON><PERSON> senester svar", "Copy Link": "Kopier link", "Copy to clipboard": "", "Copying to clipboard was successful!": "<PERSON><PERSON><PERSON> til udklipsholder!", "Create": "", "Create a knowledge base": "", "Create a model": "Lav en model", "Create Account": "<PERSON><PERSON> profil", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "<PERSON><PERSON>", "Create new key": "Opret en ny nøgle", "Create new secret key": "Opret en ny hemmelig nøgle", "Created at": "Oprettet", "Created At": "Oprettet", "Created by": "Oprettet af", "CSV Import": "Importer CSV", "Current Model": "Nuværende model", "Current Password": "Nuv<PERSON><PERSON>de password", "Custom": "Custom", "Dark": "<PERSON><PERSON><PERSON>", "Database": "Database", "December": "december", "Default": "Standard", "Default (Open AI)": "Standard (Open AI)", "Default (SentenceTransformers)": "Standard (SentenceTransformers)", "Default Model": "Standard model", "Default model updated": "Standard model opdateret", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Standardforslag til prompt", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "Brugers rolle som standard", "Delete": "Slet", "Delete a model": "Slet en model", "Delete All Chats": "Slet alle chats", "Delete All Models": "", "Delete chat": "Slet chat", "Delete Chat": "Slet chat", "Delete chat?": "Slet chat?", "Delete folder?": "", "Delete function?": "Slet funktion?", "Delete Message": "", "Delete prompt?": "Slet prompt?", "delete this link": "slet dette link", "Delete tool?": "Slet værktøj?", "Delete User": "Slet bruger", "Deleted {{deleteModelTag}}": "Slettede {{deleteModelTag}}", "Deleted {{name}}": "Slettede {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "Beskrivelse", "Disabled": "Inaktiv", "Discover a function": "Find en funktion", "Discover a model": "Find en model", "Discover a prompt": "Find en prompt", "Discover a tool": "Find et værktøj", "Discover wonders": "", "Discover, download, and explore custom functions": "Find, download og udforsk unikke funktioner", "Discover, download, and explore custom prompts": "Find, download og udforsk unikke prompts", "Discover, download, and explore custom tools": "Find, download og udforsk unikke værktøjer", "Discover, download, and explore model presets": "Find, download og udforsk modelindstillinger", "Dismissible": "<PERSON><PERSON> af<PERSON>s", "Display": "", "Display Emoji in Call": "Vis emoji i chat", "Display the username instead of You in the Chat": "Vis brugernavn i stedet for Dig i chatten", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "Lad være med at installere funktioner fra kilder, som du ikke stoler på.", "Do not install tools from sources you do not fully trust.": "Lad være med at installere værktøjer fra kilder, som du ikke stoler på.", "Document": "Dokument", "Documentation": "Dokumentation", "Documents": "Do<PERSON><PERSON><PERSON>", "does not make any external connections, and your data stays securely on your locally hosted server.": "laver ikke eksterne kald, og din data bliver sikkert på din egen lokalt hostede server.", "Don't have an account?": "Har du ikke en profil?", "don't install random functions from sources you don't trust.": "lad være med at installere tilfældige funktioner fra kilder, som du ikke stoler på.", "don't install random tools from sources you don't trust.": "lad være med at installere tilfældige værktøjer fra kilder, som du ikke stoler på.", "Done": "<PERSON><PERSON><PERSON><PERSON>", "Download": "Download", "Download canceled": "Download afbrudt", "Download Database": "Download database", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "Upload filer her for at tilføje til samtalen", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "f.eks. '30s', '10m'. <PERSON><PERSON><PERSON> værdier er 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "<PERSON><PERSON>", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "<PERSON><PERSON> hukommelse", "Edit User": "<PERSON>iger bruger", "Edit User Group": "", "ElevenLabs": "ElevenLabs", "Email": "Email", "Embark on adventures": "", "Embedding Batch Size": "Embedding <PERSON><PERSON> stø<PERSON>", "Embedding Model": "Embedding Model", "Embedding Model Engine": "Embedding Model engine", "Embedding model set to \"{{embedding_model}}\"": "Embedding model sat til \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Aktiver deling til Community", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "Aktiver rating af besked", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Aktiver nye signups", "Enable Web Search": "Aktiver websøgning", "Enabled": "Aktiveret", "Engine": "engine", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "<PERSON><PERSON><PERSON> for at din CSV-fil indeholder 4 kolonner in denne rækkefølge: Name, Email, Password, Role.", "Enter {{role}} message here": "Indtast {{role}} besked her", "Enter a detail about yourself for your LLMs to recall": "Indtast en detalje om dig selv, som dine LLMs kan huske", "Enter api auth string (e.g. username:password)": "Indtast api-godkendelsesstreng (f.eks. brugernavn:adgangskode)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Indtast Brave Search API-nøgle", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "Indtast CFG-skala (f.eks. 7.0)", "Enter Chunk Overlap": "Indtast overlapning af tekststykker", "Enter Chunk Size": "Indtast størrelse af tekststykker", "Enter description": "", "Enter Github Raw URL": "Indtast Github Raw URL", "Enter Google PSE API Key": "Indtast Google PSE API-nøgle", "Enter Google PSE Engine Id": "Indtast Google PSE Engine ID", "Enter Image Size (e.g. 512x512)": "Indtast billedstørrelse (f.eks. 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "Indtast sprogkoder", "Enter Model ID": "Indtast model-ID", "Enter model tag (e.g. {{modelTag}})": "Indtast modelmærke (f.eks. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Indtast antal trin (f.eks. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "Indtast sampler (f.eks. Euler a)", "Enter Scheduler (e.g. Karras)": "Indtast scheduler (f.eks<PERSON>)", "Enter Score": "Indtast score", "Enter SearchApi API Key": "Indtast SearchApi API-nøgle", "Enter SearchApi Engine": "Indtast SearchApi-engine", "Enter Searxng Query URL": "Indtast Searxng-forespørgsels-URL", "Enter Seed": "", "Enter Serper API Key": "Indtast Serper API-nøgle", "Enter Serply API Key": "Indtast Serply API-nøgle", "Enter Serpstack API Key": "Indtast Serpstack API-nøgle", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "Indtast stopsekvens", "Enter system prompt": "Indtast systemprompt", "Enter Tavily API Key": "Indtast Tavily API-nøgle", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "Indtast Tika Server URL", "Enter Top K": "Indtast Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "Indtast URL (f.eks. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Indtast URL (f.eks. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "Indtast din e-mail", "Enter Your Full Name": "Indtast dit fulde navn", "Enter your message": "Indtast din besked", "Enter your new password": "", "Enter Your Password": "Indtast din adgangskode", "Enter your prompt": "", "Enter Your Role": "Indtast din rolle", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "<PERSON><PERSON><PERSON>", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "Eksperimentel", "Explore the cosmos": "", "Export": "Eksportér", "Export All Archived Chats": "", "Export All Chats (All Users)": "Eksportér alle chats (alle brugere)", "Export chat (.json)": "Eksportér chat (.json)", "Export Chats": "Eksportér chats", "Export Config to JSON File": "Eksportér konfiguration til JSON-fil", "Export Functions": "Eksportér funk<PERSON>er", "Export Models": "Eksportér modeller", "Export Presets": "", "Export Prompts": "Eksportér prompts", "Export to CSV": "", "Export Tools": "Eksportér værktøjer", "External Models": "Eksterne modeller", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "Kunne ikke oprette API-nøgle.", "Failed to read clipboard contents": "Kunne ikke læse indholdet af udklipsholderen", "Failed to save models configuration": "", "Failed to update settings": "Kunne ikke opdatere indstillinger", "February": "<PERSON><PERSON><PERSON>", "Feedback History": "", "Feedbacks": "", "File": "Fil", "File added successfully.": "<PERSON><PERSON> tilfø<PERSON>.", "File content updated successfully.": "Filens indhold er opdateret.", "File Mode": "Filtilstand", "File not found.": "Filen blev ikke fundet.", "File removed successfully.": "<PERSON><PERSON> fjernet.", "File size should not exceed {{maxSize}} MB.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> må ikke overstige {{maxSize}} MB.", "File uploaded successfully": "", "Files": "Filer", "Filter is now globally disabled": "Filter er nu globalt deaktiveret", "Filter is now globally enabled": "Filter er nu globalt aktiveret", "Filters": "Filtre", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Fingeraftryksspoofing registreret: Kan ikke bruge initialer som avatar. Bruger standard profilbillede.", "Fluidly stream large external response chunks": "Stream store eksterne svar chunks flydende", "Focus chat input": "Fokuser på chatinput", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "Formular", "Format your variables using brackets like this:": "", "Frequency Penalty": "Hyppighedsstraf", "Function": "", "Function created successfully": "Funktion oprettet.", "Function deleted successfully": "<PERSON><PERSON> slettet.", "Function Description": "", "Function ID": "", "Function is now globally disabled": "Funktionen er nu globalt deaktiveret", "Function is now globally enabled": "Funktionen er nu globalt aktiveret", "Function Name": "", "Function updated successfully": "Funktion opdateret.", "Functions": "<PERSON><PERSON><PERSON>", "Functions allow arbitrary code execution": "Funktioner tillader kørsel af vilkårlig kode", "Functions allow arbitrary code execution.": "Funktioner tillader kørsel af vilkårlig kode.", "Functions imported successfully": "Funktioner importeret.", "General": "Generelt", "General Settings": "<PERSON><PERSON><PERSON>", "Generate Image": "<PERSON><PERSON>", "Generating search query": "Genererer søgeforespørgsel", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "Global", "Good Response": "<PERSON><PERSON> svar", "Google Drive": "", "Google PSE API Key": "Google PSE API-nøgle", "Google PSE Engine Id": "Google PSE Engine-ID", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "GSA Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "h:mm a": "h:mm a", "Haptic Feedback": "Haptisk feedback", "Harmful or offensive": "", "has no conversations.": "har ingen samtaler.", "Hello, {{name}}": "Hej {{name}}", "Help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "Skjul", "Host": "", "How can I help you today?": "<PERSON><PERSON><PERSON> kan jeg hjæ<PERSON>pe dig i dag?", "How would you rate this response?": "", "Hybrid Search": "Hybrid søgning", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Jeg an<PERSON>, at jeg har læst og forstået konsekvenserne af min handling. Jeg er opmærksom på de risici, der er forbundet med at udføre vilkårlig kode, og jeg har verificeret kildens troværdighed.", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "Billedgenerering (eksperimentel)", "Image Generation Engine": "Billedgenereringsengine", "Image Max Compression Size": "", "Image Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Images": "<PERSON><PERSON><PERSON>", "Import Chats": "Importer chats", "Import Config from JSON File": "Importer konfiguration fra JSON-fil", "Import Functions": "Importer funktioner", "Import Models": "Importer modeller", "Import Presets": "", "Import Prompts": "Importer prompts", "Import Tools": "Importer værktøjer", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "Inkluder `--api-auth` flag, når du kører stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Inkluder `--api` flag, når du kører stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "Info", "Input commands": "Inputkommandoer", "Install from Github URL": "Installer fra Github URL", "Instant Auto-Send After Voice Transcription": "Øjeblikkelig automatisk afsendelse efter stemmetransskription", "Interface": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Invalid file format.": "", "Invalid Tag": "Ugyldigt tag", "is typing...": "", "January": "<PERSON><PERSON><PERSON>", "Jina API Key": "", "join our Discord for help.": "tilslut dig vores Discord for at få hjælp.", "JSON": "JSON", "JSON Preview": "JSON-forhåndsvisning", "July": "<PERSON><PERSON>", "June": "<PERSON><PERSON>", "JWT Expiration": "JWT-udløb", "JWT Token": "JWT-token", "Kagi Search API Key": "", "Keep Alive": "Hold i live", "Key": "", "Keyboard shortcuts": "Tastaturgenveje", "Knowledge": "Viden", "Knowledge Access": "", "Knowledge created successfully.": "Viden oprettet.", "Knowledge deleted successfully.": "<PERSON><PERSON> slettet.", "Knowledge reset successfully.": "Viden nulstillet.", "Knowledge updated successfully": "Viden opdateret.", "Label": "", "Landing Page Mode": "Landing Page-tilstand", "Language": "Sp<PERSON>", "Last Active": "Sidst aktiv", "Last Modified": "<PERSON><PERSON> ændret", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "Lad stå tomt for ubegrænset", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "Lad stå tomt for at bruge standardprompten, eller indtast en brugerdefineret prompt", "Light": "Lys", "Listening...": "Lyt<PERSON>...", "Local": "", "Local Models": "Lokale modeller", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "Lavet af OpenWebUI Community", "Make sure to enclose them with": "<PERSON><PERSON><PERSON> for at omslutte dem med", "Make sure to export a workflow.json file as API format from ComfyUI.": "<PERSON><PERSON><PERSON> for at eksportere en workflow.json-fil som API-format fra ComfyUI.", "Manage": "Administrer", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Administrer pipelines", "March": "Marts", "Max Tokens (num_predict)": "Maks. tokens (num_predict)", "Max Upload Count": "Maks. uploadantal", "Max Upload Size": "Maks. uploadstørrelse", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Højst 3 modeller kan downloades samtidigt. Prøv igen senere.", "May": "Maj", "Memories accessible by LLMs will be shown here.": "<PERSON><PERSON>, der er tilgængelige for LLM'er, vises her.", "Memory": "Hukommelse", "Memory added successfully": "Hukommelse tilføjet.", "Memory cleared successfully": "Hukommelse ryddet.", "Memory deleted successfully": "Hu<PERSON><PERSON><PERSON><PERSON> slettet.", "Memory updated successfully": "Hukommelse opdateret.", "Merge Responses": "<PERSON><PERSON> svar", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "<PERSON><PERSON><PERSON>, du sender efter at have oprettet dit link, deles ikke. Brugere med URL'en vil kunne se den delte chat.", "Min P": "<PERSON>", "Minimum Score": "Minimumscore", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "MMMM DD, YYYY hh:mm:ss A", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "Model '{{modelName}}' er blevet downloadet.", "Model '{{modelTag}}' is already in queue for downloading.": "Model '{{modelTag}}' er allerede i kø til download.", "Model {{modelId}} not found": "Model {{modelId}} ikke fundet", "Model {{modelName}} is not vision capable": "Model {{modelName}} under<PERSON><PERSON><PERSON> ik<PERSON>er", "Model {{name}} is now {{status}}": "Model {{name}} er nu {{status}}", "Model accepts image inputs": "Model accepterer billedinput", "Model created successfully!": "Model oprettet!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Model filsystemsti registreret. Modelkortnavn er påkrævet til opdatering, kan ikke fortsætte.", "Model Filtering": "", "Model ID": "Model-ID", "Model IDs": "", "Model Name": "", "Model not selected": "Model ikke valgt", "Model Params": "Modelparametre", "Model Permissions": "", "Model updated successfully": "Model opdateret.", "Modelfile Content": "Modelfilindhold", "Models": "Modeller", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "<PERSON><PERSON>", "Name": "Navn", "Name your knowledge base": "", "New Chat": "Ny chat", "New folder": "", "New Password": "Ny adgangskode", "new-channel": "", "No content found": "", "No content to speak": "Intet indhold at tale", "No distance available": "", "No feedbacks found": "", "No file selected": "Ingen fil valgt", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "Intet HTML-, CSS- eller JavaScript-indhold fundet.", "No knowledge found": "Ingen viden fundet", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "Ingen resultater fundet", "No search query generated": "Ingen søgeforespørgsel genereret", "No source available": "<PERSON>gen kilde <PERSON>", "No users were found.": "", "No valves to update": "Ingen ventiler at opdatere", "None": "Ingen", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Bemærk: <PERSON><PERSON> du angiver en minimumscore, returnerer søgningen kun dokumenter med en score, der er større end eller lig med minimumscoren.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Notifikationer", "November": "November", "num_gpu (Ollama)": "num_gpu (Ollama)", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "OAuth-ID", "October": "Oktober", "Off": "<PERSON>a", "Okay, Let's Go!": "Okay, lad os gå!", "OLED Dark": "OLED Mørk", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "Ollama API deaktiveret", "Ollama API settings updated": "", "Ollama Version": "Ollama-version", "On": "Til", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Kun alfanumeriske tegn og bindestreger er tilladt i kommandostrengen.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "<PERSON>n samlinger kan redigeres, opret en ny vidensbase for at redigere/tilføje dokumenter.", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Ups! URL'en ser ud til at være ugyldig. Tjek den igen, og prøv igen.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Ups! Du bruger en metode, der ikke understøttes (kun frontend). Kør WebUI fra backend.", "Open in full screen": "Åbn i fuld skærm", "Open new chat": "Åbn ny chat", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open WebUI-version (v{{OPEN_WEBUI_VERSION}}) er lavere end den krævede version (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API-konfiguration", "OpenAI API Key is required.": "OpenAI API-nøgle er påkrævet.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "OpenAI URL/nøgle påkrævet.", "or": "eller", "Organize your users": "", "OUTPUT": "", "Output format": "Outputformat", "Overview": "Oversigt", "page": "side", "Password": "Adgangskode", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF-dokument (.pdf)", "PDF Extract Images (OCR)": "Udtræk billeder fra PDF (OCR)", "pending": "a<PERSON><PERSON><PERSON>", "Permission denied when accessing media devices": "Tilladelse nægtet ved adgang til medieenheder", "Permission denied when accessing microphone": "Tilladelse nægtet ved adgang til mikrofon", "Permission denied when accessing microphone: {{error}}": "Tilladelse nægtet ved adgang til mikrofon: {{error}}", "Permissions": "", "Personalization": "Personalisering", "Pin": "Fastgør", "Pinned": "Fastgjort", "Pioneer insights": "", "Pipeline deleted successfully": "Pipeline slettet.", "Pipeline downloaded successfully": "Pipeline downloadet.", "Pipelines": "Pipelines", "Pipelines Not Detected": "Pipelines ikke registreret", "Pipelines Valves": "Pipelines-ventiler", "Plain text (.txt)": "<PERSON><PERSON><PERSON><PERSON> te<PERSON> (.txt)", "Playground": "Legeplads", "Please carefully review the following warnings:": "Gennemgå omhyggeligt følge<PERSON> advar<PERSON>:", "Please enter a prompt": "", "Please fill in all fields.": "<PERSON><PERSON><PERSON><PERSON> alle felter.", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "Seneste 30 dage", "Previous 7 days": "Seneste 7 dage", "Profile Image": "<PERSON><PERSON><PERSON><PERSON>", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (f.eks. <PERSON> mig en sjov kendsgerning om Romerriget)", "Prompt Content": "Prompt<PERSON><PERSON>", "Prompt created successfully": "", "Prompt suggestions": "Promptforslag", "Prompt updated successfully": "", "Prompts": "Prompts", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "Hent \"{{searchValue}}\" fra Ollama.com", "Pull a model from Ollama.com": "Hent en model fra Ollama.com", "Query Generation Prompt": "", "Query Params": "Forespørgselsparametre", "RAG Template": "RAG-skabelon", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "<PERSON><PERSON><PERSON>", "Record voice": "Optag stemme", "Redirecting you to OpenWebUI Community": "Omdirigerer dig til OpenWebUI Community", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "<PERSON><PERSON><PERSON> til dig selv som \"Bruger\" (f.eks. \"Bruger lærer spansk\")", "References from": "", "Refresh Token Expiration": "", "Regenerate": "<PERSON><PERSON><PERSON>", "Release Notes": "Udgivelsesnoter", "Relevance": "", "Remove": "<PERSON><PERSON><PERSON>", "Remove Model": "Fjern model", "Rename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reorder Models": "", "Repeat Last N": "Gentag sidste N", "Reply in Thread": "", "Request Mode": "Forespørgselstilstand", "Reranking Model": "Omarrangeringsmodel", "Reranking model disabled": "Omarrangeringsmodel deaktiveret", "Reranking model set to \"{{reranking_model}}\"": "<PERSON><PERSON><PERSON><PERSON><PERSON> sat til \"{{reranking_model}}\"", "Reset": "Nulstil", "Reset All Models": "", "Reset Upload Directory": "Nulstil uploadmappe", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Svarnotifikationer kan ikke aktiveres, da webstedets tilladelser er blevet nægtet. <PERSON><PERSON><PERSON>g dine browserindstillinger for at give den nødvendige adgang.", "Response splitting": "Svaropdeling", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "<PERSON><PERSON>", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "<PERSON><PERSON><PERSON>", "Running": "<PERSON><PERSON><PERSON>", "Save": "Gem", "Save & Create": "Gem og opret", "Save & Update": "Gem og opdater", "Save As Copy": "Gem som kopi", "Save Tag": "Gem tag", "Saved": "Gemt", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Lagring af chatlogs direkte i din browsers lager understøttes ikke længere. Download og slet dine chatlogs ved at klikke på knappen nedenfor. Du kan nemt importere dine chatlogs til backend igennem", "Scroll to bottom when switching between branches": "<PERSON><PERSON> til bunden, når du skifter mellem grene", "Search": "<PERSON><PERSON><PERSON>", "Search a model": "<PERSON><PERSON><PERSON> efter en model", "Search Base": "", "Search Chats": "<PERSON>øg i chats", "Search Collection": "Søg i samling", "Search Filters": "", "search for tags": "", "Search Functions": "Søg i funktioner", "Search Knowledge": "Søg i viden", "Search Models": "Søg i modeller", "Search options": "", "Search Prompts": "<PERSON><PERSON>g i prompts", "Search Result Count": "Antal søgeresultater", "Search Tools": "Søg i værktøjer", "Search users": "", "SearchApi API Key": "SearchApi API-nøgle", "SearchApi Engine": "SearchApi-engine", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> efter \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> i viden efter \"{{searchQuery}}\"", "Searxng Query URL": "Searxng forespørgsels-URL", "See readme.md for instructions": "Se readme.md for instruktioner", "See what's new": "Se, hvad der er nyt", "Seed": "Seed", "Select a base model": "Vælg en basemodel", "Select a engine": "Vælg en engine", "Select a function": "<PERSON>æ<PERSON><PERSON> en funktion", "Select a group": "", "Select a model": "Vælg en model", "Select a pipeline": "Vælg en pipeline", "Select a pipeline url": "Vælg en pipeline-URL", "Select a tool": "Vælg et værktøj", "Select Engine": "Vælg engine", "Select Knowledge": "<PERSON><PERSON><PERSON><PERSON> viden", "Select model": "Vælg model", "Select only one model to call": "<PERSON><PERSON><PERSON><PERSON> kun én model at kalde", "Selected model(s) do not support image inputs": "Valgte model(ler) understøtter ikke billed<PERSON>put", "Semantic distance to query": "", "Send": "Send", "Send a message": "", "Send a Message": "Send en besked", "Send message": "Send besked", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Sender `stream_options: { include_usage: true }` i forespørgslen.\nUnderstøttede udbydere vil returnere tokenforbrugsinformation i svaret, når det er indstillet.", "September": "September", "Serper API Key": "Serper API-nøgle", "Serply API Key": "Serply API-nøgle", "Serpstack API Key": "Serpstack API-nøgle", "Server connection verified": "Serverforbindelse bekræftet", "Set as default": "Indstil som standard", "Set CFG Scale": "Indstil CFG-skala", "Set Default Model": "Indstil standardmodel", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Indstil indlejringsmodel (f.eks. {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "Indstil omarrangeringsmodel (f.eks. {{model}})", "Set Sampler": "Indstil sampler", "Set Scheduler": "Indstil scheduler", "Set Steps": "Indstil trin", "Set Task Model": "Indstil opgavemodel", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "Indstil stemme", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "<PERSON><PERSON><PERSON><PERSON>", "Settings saved successfully!": "Indstillinger gemt!", "Share": "Del", "Share Chat": "Del chat", "Share to OpenWebUI Community": "Del til OpenWebUI Community", "Show": "Vis", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "Vis <PERSON><PERSON><PERSON><PERSON> i overlay for ventende konto", "Show shortcuts": "<PERSON>is genveje", "Show your support!": "Vis din støtte!", "Sign in": "Log ind", "Sign in to {{WEBUI_NAME}}": "Log ind på {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "Log ud", "Sign up": "Tilmeld dig", "Sign up to {{WEBUI_NAME}}": "Tilmeld dig {{WEBUI_NAME}}", "Signing in to {{WEBUI_NAME}}": "Logger ind på {{WEBUI_NAME}}", "sk-1234": "", "Source": "<PERSON><PERSON>", "Speech Playback Speed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Speech recognition error: {{error}}": "Talegenkendelsesfejl: {{error}}", "Speech-to-Text Engine": "Tale-til-tekst-engine", "Stop": "", "Stop Sequence": "Stopsekvens", "Stream Chat Response": "Stream chatsvar", "STT Model": "STT-model", "STT Settings": "STT-indstillinger", "Success": "Succes", "Successfully updated.": "Opdateret.", "Suggested prompts to get you started": "", "Support": "Support", "Support this plugin:": "<PERSON><PERSON><PERSON> dette plugin:", "Sync directory": "Synkroniser mappe", "System": "System", "System Instructions": "", "System Prompt": "Systemprompt", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "Tryk for at afbryde", "Tavily API Key": "Tavily API-nøgle", "Temperature": "Temperatur", "Template": "Skabelon", "Temporary Chat": "<PERSON><PERSON><PERSON>dig chat", "Text Splitter": "", "Text-to-Speech Engine": "Tekst-til-tale-engine", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Tak for din feedback!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Udviklerne bag dette plugin er passionerede frivillige fra fællesskabet. Hvis du finder dette plugin nyttigt, kan du overveje at bidrage til dets udvikling.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Den maksimale filstørrelse i MB. Hvis filstørrelsen overstiger denne græ<PERSON>, uploades filen ikke.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Det maksimale antal filer, der kan bruges på én gang i chatten. Hvis antallet af filer overstiger denne græ<PERSON>, uploades filerne ikke.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Scoren skal være en værdi mellem 0,0 (0%) og 1,0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "<PERSON><PERSON>", "Thinking...": "Tæ<PERSON><PERSON>...", "This action cannot be undone. Do you wish to continue?": "<PERSON><PERSON> handling kan ikke fortrydes. Vil du fortsætte?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON><PERSON> si<PERSON>, at dine værdifulde samtaler gemmes sikkert i din backend-database. Tak!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Dette er en eksperimentel funktion, den fungerer muligvis ikke som forventet og kan ændres når som helst.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "<PERSON>ne indstilling sletter alle eksisterende filer i samlingen og erstatter dem med nyligt uploadede filer.", "This response was generated by \"{{model}}\"": "", "This will delete": "<PERSON><PERSON> vil slette", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Dette vil nulstille vidensbasen og synkronisere alle filer. Vil du fortsætte?", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Tika-server-URL påkrævet.", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tip: <PERSON><PERSON><PERSON> flere variabelpladser fortløbende ved at trykke på tabulatortasten i chatinput efter hver udskiftning.", "Title": "Titel", "Title (e.g. Tell me a fun fact)": "Titel (f.eks. <PERSON> mig en sjov kendsgerning)", "Title Auto-Generation": "Automatisk titelgenerering", "Title cannot be an empty string.": "Titel kan ikke være en tom streng.", "Title Generation Prompt": "Prompt til titelge<PERSON>ering", "TLS": "", "To access the available model names for downloading,": "For at få adgang til de tilgængelige modelnavne til download,", "To access the GGUF models available for downloading,": "For at få adgang til de GGUF-modeller, der er tilgængelige til download,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "For at få adgang til WebUI skal du kontakte administratoren. Administratorer kan administrere brugerstatus fra administrationspanelet.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "For at vedhæfte vidensbase her skal du først tilføje dem til \"Viden\"-arbejdsområdet.", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "For at vælge handlinger her skal du først tilføje dem til \"Funktioner\"-arbejdsområdet.", "To select filters here, add them to the \"Functions\" workspace first.": "For at vælge filtre her skal du først tilføje dem til \"Funktioner\"-arbejdsområdet.", "To select toolkits here, add them to the \"Tools\" workspace first.": "For at vælge værktøjssæt her skal du først tilføje dem til \"Værktøjer\"-arbejdsområdet.", "Toast notifications for new updates": "", "Today": "I dag", "Toggle settings": "<PERSON><PERSON> indstillinger", "Toggle sidebar": "Skift sidebjælke", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "<PERSON><PERSON><PERSON>, der skal beholdes ved kontekstopdatering (num_keep)", "Tool created successfully": "Værktøj oprettet.", "Tool deleted successfully": "<PERSON>ærk<PERSON><PERSON><PERSON> s<PERSON>t.", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "Værktøj importeret.", "Tool Name": "", "Tool updated successfully": "Værktøj opdateret.", "Tools": "<PERSON><PERSON><PERSON>tøjer", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "Værktøjer er et funktionkaldssystem med vilkårlig kodeudførelse", "Tools have a function calling system that allows arbitrary code execution": "Værktøjer har et funktionkaldssystem, der tillader vilkårlig kodeudførelse", "Tools have a function calling system that allows arbitrary code execution.": "Værktøjer har et funktionkaldssystem, der tillader vilkårlig kodeudførelse.", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "<PERSON>er med at få adgang til Ollama?", "TTS Model": "TTS-model", "TTS Settings": "TTS-inds<PERSON>linger", "TTS Voice": "TTS-stemme", "Type": "Type", "Type Hugging Face Resolve (Download) URL": "Indtast Hugging Face Resolve (Download) URL", "Uh-oh! There was an issue with the response.": "", "UI": "UI", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "Frig<PERSON><PERSON>", "Unravel secrets": "", "Untagged": "", "Update": "Opdater", "Update and Copy Link": "Opdater og kopier link", "Update for the latest features and improvements.": "<PERSON><PERSON>r for at få de nyeste funktioner og forbedringer.", "Update password": "Opdater adgangskode", "Updated": "", "Updated at": "Opdateret kl.", "Updated At": "", "Upload": "Upload", "Upload a GGUF model": "Upload en GGUF-model", "Upload directory": "Uploadmappe", "Upload files": "Upload filer", "Upload Files": "Upload filer", "Upload Pipeline": "Upload pipeline", "Upload Progress": "Uploadfremdrift", "URL": "", "URL Mode": "URL-tilstand", "Use '#' in the prompt input to load and include your knowledge.": "Brug '#' i promptinput for at indlæse og inkludere din viden.", "Use Gravatar": "Brug Gravatar", "Use groups to group your users and assign permissions.": "", "Use Initials": "Brug initialer", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "bruger", "User": "", "User location successfully retrieved.": "Brugerplacering hentet.", "Username": "", "Users": "Brugere", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "<PERSON><PERSON><PERSON>", "Valid time units:": "Gyldi<PERSON> tids<PERSON>er:", "Valves": "<PERSON><PERSON><PERSON>", "Valves updated": "Ventiler opdateret", "Valves updated successfully": "Ventiler opdateret.", "variable": "variabel", "variable to have them replaced with clipboard content.": "variabel for at få dem erstattet med indholdet af udklipsholderen.", "Version": "Version", "Version {{selectedVersion}} of {{totalVersions}}": "Version {{selectedVersion}} af {{totalVersions}}", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "<PERSON><PERSON><PERSON>", "Voice Input": "", "Warning": "<PERSON><PERSON><PERSON>", "Warning:": "<PERSON><PERSON><PERSON>:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Advarsel: <PERSON><PERSON> du opdaterer eller ændrer din indlejringsmodel, skal du importere alle dokumenter igen.", "Web": "Web", "Web API": "Web API", "Web Loader Settings": "Web Loader-indstillinger", "Web Search": "Websøgning", "Web Search Engine": "Websøgemaskine", "Web Search Query Generation": "", "Webhook URL": "Webhook-URL", "WebUI Settings": "WebUI-indstillinger", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "Nyheder i", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "Whisper (lokal)", "Widescreen Mode": "Widescreen-tilstand", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "Arbejdsområde", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "Skriv et promptforslag (f.eks. Hvem er du?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Skriv en opsummering på 50 ord, der opsummerer [emne eller sø<PERSON>].", "Write something...": "", "Write your model template content here": "", "Yesterday": "<PERSON> går", "You": "<PERSON>", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Du kan kun chatte med maksimalt {{maxCount}} fil(er) ad gangen.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Du kan personliggøre dine interaktioner med LLM'er ved at tilføje minder via knappen 'Administrer' nedenfor, hvilket gør dem mere nyttige og skræddersyet til dig.", "You cannot upload an empty file.": "", "You have no archived conversations.": "Du har ingen arkiverede samtaler.", "You have shared this chat": "Du har delt denne chat", "You're a helpful assistant.": "Du er en hjælpsom assistent.", "Your account status is currently pending activation.": "Din kontostatus afventer i øjeblikket aktivering.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "<PERSON><PERSON> dit bidrag går direkte til plugin-udvikleren; Open WebUI tager ikke nogen procentdel. Den valgte finansieringsplatform kan dog have sine egne gebyrer.", "Youtube": "Youtube", "Youtube Loader Settings": "Youtube Loader-indstillinger"}