{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' o '-1' per nessuna scadenza.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(p.e. `sh webui.sh --api`)", "(latest)": "(ultima)", "{{ models }}": "{{ modelli }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}} Chat", "{{webUIName}} Backend Required": "{{webUIName}} Backend richiesto", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Un modello di attività viene utilizzato durante l'esecuzione di attività come la generazione di titoli per chat e query di ricerca Web", "a user": "un utente", "About": "Informazioni", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "Account", "Account Activation Pending": "", "Actions": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "", "Add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Add a model ID": "", "Add a short description about what this model does": "Aggiungi una breve descrizione di ciò che fa questo modello", "Add a tag": "Aggiungi un tag", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "Aggiungi un prompt custom", "Add Files": "Aggiungi file", "Add Group": "", "Add Memory": "Aggiungi memoria", "Add Model": "Aggiungi modello", "Add Reaction": "", "Add Tag": "", "Add Tags": "Aggiungi tag", "Add text content": "", "Add User": "Aggiungi utente", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "La modifica di queste impostazioni applicherà le modifiche universalmente a tutti gli utenti.", "admin": "amministratore", "Admin": "", "Admin Panel": "Pannello di amministrazione", "Admin Settings": "Impostazioni amministratore", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "<PERSON><PERSON><PERSON>", "Advanced Params": "<PERSON><PERSON><PERSON>", "All Documents": "Tutti i documenti", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "Consenti l'eliminazione della chat", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "", "Allow Temporary Chat": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Already have an account?": "Hai già un account?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "un assistente", "and": "e", "and {{COUNT}} more": "", "and create a new shared link.": "e crea un nuovo link condiviso.", "API Base URL": "URL base API", "API Key": "Chiave API", "API Key created.": "Chiave API creata.", "API Key Endpoint Restrictions": "", "API keys": "Chiavi API", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "<PERSON>e", "Archive": "Archivio", "Archive All Chats": "<PERSON><PERSON><PERSON> tutte le chat", "Archived Chats": "Chat archiviate", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "Sei sicuro?", "Arena Models": "", "Artifacts": "", "Ask a question": "", "Assistant": "", "Attach file": "Allega file", "Attribute for Username": "", "Audio": "Audio", "August": "Agosto", "Authenticate": "", "Auto-Copy Response to Clipboard": "Copia automatica della risposta negli appunti", "Auto-playback response": "Riproduzione automatica della risposta", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "URL base AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "L'URL base AUTOMATIC1111 è obbligatorio.", "Available list": "", "available!": "disponibile!", "Azure AI Speech": "", "Azure Region": "", "Back": "Indietro", "Bad": "", "Bad Response": "Risposta non valida", "Banners": "Banner", "Base Model (From)": "Modello base (da)", "Batch Size (num_batch)": "", "before": "prima", "Beta": "", "BETA": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Chiave API di ricerca Brave", "By {{name}}": "", "Bypass SSL verification for Websites": "Aggira la verifica SSL per i siti web", "Call": "", "Call feature is not supported when using Web STT engine": "", "Camera": "", "Cancel": "<PERSON><PERSON><PERSON>", "Capabilities": "Funzionalità", "Capture": "", "Certificate Path": "", "Change Password": "Cambia password", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "Cha<PERSON>", "Chat Background Image": "", "Chat Bubble UI": "UI bolle chat", "Chat Controls": "", "Chat direction": "<PERSON><PERSON><PERSON> chat", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "Cha<PERSON>", "Check Again": "Controlla di nuovo", "Check for updates": "Controlla aggiornamenti", "Checking for updates...": "Controllo aggiornamenti...", "Choose a model before saving...": "Scegli un modello prima di salvare...", "Chunk Overlap": "Sovrapposizione chunk", "Chunk Params": "Parametri chunk", "Chunk Size": "Dimensione chunk", "Ciphers": "", "Citation": "Citazione", "Clear memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "Clicca qui per aiuto.", "Click here to": "Clicca qui per", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "<PERSON>lic<PERSON> qui per selezionare", "Click here to select a csv file.": "Clicca qui per selezionare un file csv.", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "clicca qui.", "Click on the user role button to change a user's role.": "Clicca sul pulsante del ruolo utente per modificare il ruolo di un utente.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "<PERSON><PERSON>", "Close": "<PERSON><PERSON>", "Code execution": "", "Code formatted successfully": "", "Collection": "Collezione", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "URL base ComfyUI", "ComfyUI Base URL is required.": "L'URL base ComfyUI è obbligatorio.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "Comand<PERSON>", "Completions": "", "Concurrent Requests": "Richieste simultanee", "Configure": "", "Configure Models": "", "Confirm": "", "Confirm Password": "Conferma password", "Confirm your action": "", "Confirm your new password": "", "Connections": "<PERSON><PERSON><PERSON><PERSON>", "Contact Admin for WebUI Access": "", "Content": "<PERSON><PERSON><PERSON>", "Content Extraction": "", "Context Length": "Lunghezza contesto", "Continue Response": "Continua risposta", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "", "Copied shared chat URL to clipboard!": "URL della chat condivisa copiato negli appunti!", "Copied to clipboard": "", "Copy": "Copia", "Copy last code block": "Copia ultimo blocco di codice", "Copy last response": "Copia ultima risposta", "Copy Link": "Copia link", "Copy to clipboard": "", "Copying to clipboard was successful!": "Copia negli appunti riuscita!", "Create": "", "Create a knowledge base": "", "Create a model": "<PERSON><PERSON><PERSON> un modello", "Create Account": "Crea account", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "Crea nuova chiave", "Create new secret key": "Crea nuova chiave segreta", "Created at": "Creato il", "Created At": "Creato il", "Created by": "", "CSV Import": "", "Current Model": "<PERSON><PERSON>", "Current Password": "Password corrente", "Custom": "<PERSON><PERSON><PERSON><PERSON>", "Dark": "<PERSON><PERSON>", "Database": "Database", "December": "Dicembre", "Default": "Predefinito", "Default (Open AI)": "", "Default (SentenceTransformers)": "Predefinito (SentenceTransformers)", "Default Model": "<PERSON>lo di default", "Default model updated": "<PERSON><PERSON> predefinito a<PERSON>", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Suggerimenti prompt predefiniti", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "<PERSON><PERSON>lo utente predefinito", "Delete": "Elimina", "Delete a model": "Elimina un modello", "Delete All Chats": "<PERSON>mina tutte le chat", "Delete All Models": "", "Delete chat": "Elimina chat", "Delete Chat": "Elimina chat", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete prompt?": "", "delete this link": "elimina questo link", "Delete tool?": "", "Delete User": "Elimina utente", "Deleted {{deleteModelTag}}": "Eliminato {{deleteModelTag}}", "Deleted {{name}}": "Eliminato {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "Descrizione", "Disabled": "", "Discover a function": "", "Discover a model": "Scopri un modello", "Discover a prompt": "<PERSON><PERSON><PERSON> un prompt", "Discover a tool": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON>, scarica ed esplora prompt personal<PERSON><PERSON><PERSON>", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON>, scarica ed esplora i preset del modello", "Dismissible": "", "Display": "", "Display Emoji in Call": "", "Display the username instead of You in the Chat": "Visualizza il nome utente invece di Tu nella chat", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Document": "Documento", "Documentation": "", "Documents": "Documenti", "does not make any external connections, and your data stays securely on your locally hosted server.": "non effettua connessioni esterne e i tuoi dati rimangono al sicuro sul tuo server ospitato localmente.", "Don't have an account?": "Non hai un account?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Done": "", "Download": "Scarica", "Download canceled": "Scaricamento annullato", "Download Database": "Scarica database", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "Trascina qui i file da aggiungere alla conversazione", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "ad esempio '30s','10m'. Le unità di tempo valide sono 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "Modifica", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "", "Edit User": "Modifica utente", "Edit User Group": "", "ElevenLabs": "", "Email": "Email", "Embark on adventures": "", "Embedding Batch Size": "", "Embedding Model": "<PERSON><PERSON> di embedding", "Embedding Model Engine": "Motore del modello di embedding", "Embedding model set to \"{{embedding_model}}\"": "<PERSON><PERSON> di embedding impostato su \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Abilita la condivisione della community", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Abilita nuove iscrizioni", "Enable Web Search": "Abilita ricerca Web", "Enabled": "", "Engine": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Assicurati che il tuo file CSV includa 4 colonne in questo ordine: <PERSON>me, Email, Password, <PERSON><PERSON><PERSON>.", "Enter {{role}} message here": "Inser<PERSON>ci il messaggio per {{role}} qui", "Enter a detail about yourself for your LLMs to recall": "Inserisci un dettaglio su di te per che i LLM possano ricordare", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Inserisci la chiave API di Brave Search", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "Inserisci la sovrapposizione chunk", "Enter Chunk Size": "Inserisci la dimensione chunk", "Enter description": "", "Enter Github Raw URL": "Immettere l'URL grezzo di Github", "Enter Google PSE API Key": "Inserisci la chiave API PSE di Google", "Enter Google PSE Engine Id": "Inserisci l'ID motore PSE di Google", "Enter Image Size (e.g. 512x512)": "Inserisci la dimensione dell'immagine (ad esempio 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "Inserisci i codici lingua", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "Inserisci il tag del modello (ad esempio {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Inserisci il numero di passaggi (ad esempio 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "Inserisci il punteggio", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Immettere l'URL della query Searxng", "Enter Seed": "", "Enter Serper API Key": "Inserisci la chiave API Serper", "Enter Serply API Key": "", "Enter Serpstack API Key": "Inserisci la chiave API Serpstack", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "Inserisci la sequenza di arresto", "Enter system prompt": "", "Enter Tavily API Key": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "", "Enter Top K": "Inserisci Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "Inserisci URL (ad esempio http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Inserisci URL (ad esempio http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "Inserisci la tua email", "Enter Your Full Name": "Inserisci il tuo nome completo", "Enter your message": "", "Enter your new password": "", "Enter Your Password": "Inserisci la tua password", "Enter your prompt": "", "Enter Your Role": "Inser<PERSON><PERSON> il tuo ruolo", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "Errore", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "Sperimentale", "Explore the cosmos": "", "Export": "Esportazione", "Export All Archived Chats": "", "Export All Chats (All Users)": "Esporta tutte le chat (tutti gli utenti)", "Export chat (.json)": "", "Export Chats": "Esporta chat", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "Esporta modelli", "Export Presets": "", "Export Prompts": "Esporta prompt", "Export to CSV": "", "Export Tools": "", "External Models": "", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "Impossibile creare la chiave API.", "Failed to read clipboard contents": "Impossibile leggere il contenuto degli appunti", "Failed to save models configuration": "", "Failed to update settings": "", "February": "<PERSON><PERSON><PERSON>", "Feedback History": "", "Feedbacks": "", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "Modalità file", "File not found.": "File non trovato.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "R<PERSON>vato spoofing delle impronte digitali: impossibile utilizzare le iniziali come avatar. Ripristino all'immagine del profilo predefinita.", "Fluidly stream large external response chunks": "<PERSON><PERSON><PERSON><PERSON> in modo fluido blocchi di risposta esterni di grandi dimensioni", "Focus chat input": "<PERSON><PERSON> a fuoco l'input della chat", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Frequency Penalty": "Penalità di frequenza", "Function": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "General": "Generale", "General Settings": "Impostazioni generali", "Generate Image": "", "Generating search query": "Generazione di query di ricerca", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "<PERSON><PERSON><PERSON> ris<PERSON>a", "Google Drive": "", "Google PSE API Key": "Chiave API PSE di Google", "Google PSE Engine Id": "ID motore PSE di Google", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "GSA Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "h:mm a": "h:mm a", "Haptic Feedback": "", "Harmful or offensive": "", "has no conversations.": "non ha conversazioni.", "Hello, {{name}}": "<PERSON><PERSON><PERSON>, {{name}}", "Help": "<PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "Nascondi", "Host": "", "How can I help you today?": "Come posso aiutarti oggi?", "How would you rate this response?": "", "Hybrid Search": "Ricerca ibrida", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "Generazione di immagini (sperimentale)", "Image Generation Engine": "Motore di generazione immagini", "Image Max Compression Size": "", "Image Settings": "Impostazioni immagine", "Images": "<PERSON><PERSON><PERSON><PERSON>", "Import Chats": "Importa chat", "Import Config from JSON File": "", "Import Functions": "", "Import Models": "Importazione di modelli", "Import Presets": "", "Import Prompts": "Importa prompt", "Import Tools": "", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "<PERSON>ludi il flag `--api` quando esegui stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "Informazioni", "Input commands": "Comandi di input", "Install from Github URL": "Eseguire l'installazione dall'URL di Github", "Instant Auto-Send After Voice Transcription": "", "Interface": "Interfaccia", "Invalid file format.": "", "Invalid Tag": "Tag non valido", "is typing...": "", "January": "Gennaio", "Jina API Key": "", "join our Discord for help.": "unisciti al nostro Discord per ricevere aiuto.", "JSON": "JSON", "JSON Preview": "Anteprima JSON", "July": "<PERSON><PERSON><PERSON>", "June": "<PERSON><PERSON><PERSON>", "JWT Expiration": "Scadenza JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "", "Keep Alive": "Man<PERSON>i attivo", "Key": "", "Keyboard shortcuts": "Scorciatoie da tastiera", "Knowledge": "", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "<PERSON><PERSON>", "Last Active": "Ultima attività", "Last Modified": "", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "Chiaro", "Listening...": "", "Local": "", "Local Models": "", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "Realizzato dalla comunità OpenWebUI", "Make sure to enclose them with": "Assicurati di racchiuderli con", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Gestire le pipeline", "March": "<PERSON><PERSON>", "Max Tokens (num_predict)": "Numero massimo di gettoni (num_predict)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "È possibile scaricare un massimo di 3 modelli contemporaneamente. Riprova più tardi.", "May": "Maggio", "Memories accessible by LLMs will be shown here.": "I memori accessibili ai LLM saranno mostrati qui.", "Memory": "Memoria", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "I messaggi inviati dopo la creazione del link non verranno condivisi. Gli utenti con l'URL saranno in grado di visualizzare la chat condivisa.", "Min P": "", "Minimum Score": "Punteggio minimo", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "Il modello '{{modelName}}' è stato scaricato con successo.", "Model '{{modelTag}}' is already in queue for downloading.": "Il modello '{{modelTag}}' è già in coda per il download.", "Model {{modelId}} not found": "<PERSON>lo {{modelId}} non trovato", "Model {{modelName}} is not vision capable": "Il modello {{modelName}} non è in grado di vedere", "Model {{name}} is now {{status}}": "Il modello {{name}} è ora {{status}}", "Model accepts image inputs": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Percorso del filesystem del modello rilevato. Il nome breve del modello è richiesto per l'aggiornamento, impossibile continuare.", "Model Filtering": "", "Model ID": "ID modello", "Model IDs": "", "Model Name": "", "Model not selected": "Modello non selezionato", "Model Params": "Parametri del modello", "Model Permissions": "", "Model updated successfully": "", "Modelfile Content": "Contenuto del file modello", "Models": "<PERSON><PERSON>", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "Altro", "Name": "Nome", "Name your knowledge base": "", "New Chat": "Nuova chat", "New folder": "", "New Password": "Nuova password", "new-channel": "", "No content found": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato", "No search query generated": "Nessuna query di ricerca generata", "No source available": "Nessuna fonte disponibile", "No users were found.": "", "No valves to update": "", "None": "<PERSON><PERSON><PERSON>", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Nota: se imposti un punteggio minimo, la ricerca restituirà solo i documenti con un punteggio maggiore o uguale al punteggio minimo.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Notifiche desktop", "November": "Novembre", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "", "October": "Ottobre", "Off": "Disattivat<PERSON>", "Okay, Let's Go!": "Ok, andiamo!", "OLED Dark": "OLED scuro", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "API Ollama disabilitata", "Ollama API settings updated": "", "Ollama Version": "<PERSON><PERSON>", "On": "<PERSON><PERSON><PERSON><PERSON>", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Nella stringa di comando sono consentiti solo caratteri alfanumerici e trattini.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Ops! Sembra che l'URL non sia valido. Si prega di ricontrollare e riprovare.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Ops! Stai utilizzando un metodo non supportato (solo frontend). Si prega di servire la WebUI dal backend.", "Open in full screen": "", "Open new chat": "Apri nuova chat", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Configurazione API OpenAI", "OpenAI API Key is required.": "La chiave API OpenAI è obbligatoria.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "URL/Chiave OpenAI obbligatori.", "or": "o", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "Password", "Paste Large Text as File": "", "PDF document (.pdf)": "Documento PDF (.pdf)", "PDF Extract Images (OCR)": "Estrazione immagini PDF (OCR)", "pending": "in sospeso", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "Autorizzazione negata durante l'accesso al microfono: {{error}}", "Permissions": "", "Personalization": "Personalizzazione", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "Condutture", "Pipelines Not Detected": "", "Pipelines Valves": "Valvole per tubazioni", "Plain text (.txt)": "Testo normale (.txt)", "Playground": "Terreno di gioco", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "Ultimi 30 giorni", "Previous 7 days": "Ultimi 7 giorni", "Profile Image": "Immagine del profilo", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (ad esempio Dimmi un fatto divertente sull'Impero Romano)", "Prompt Content": "Contenuto del prompt", "Prompt created successfully": "", "Prompt suggestions": "<PERSON>gger<PERSON><PERSON> prompt", "Prompt updated successfully": "", "Prompts": "Prompt", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "E<PERSON>i \"{{searchValue}}\" da Ollama.com", "Pull a model from Ollama.com": "Estrai un modello da Ollama.com", "Query Generation Prompt": "", "Query Params": "Parametri query", "RAG Template": "Modello RAG", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "Leggi ad alta voce", "Record voice": "Registra voce", "Redirecting you to OpenWebUI Community": "Reindirizzamento alla comunità OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refresh Token Expiration": "", "Regenerate": "<PERSON><PERSON><PERSON><PERSON>", "Release Notes": "Note di rilascio", "Relevance": "", "Remove": "<PERSON><PERSON><PERSON><PERSON>", "Remove Model": "<PERSON><PERSON><PERSON><PERSON>lo", "Rename": "Rinomina", "Reorder Models": "", "Repeat Last N": "Ripeti ultimi N", "Reply in Thread": "", "Request Mode": "Modalità richiesta", "Reranking Model": "Modello di riclassificazione", "Reranking model disabled": "Modello di riclassificazione disabilitato", "Reranking model set to \"{{reranking_model}}\"": "Modello di riclassificazione impostato su \"{{reranking_model}}\"", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "<PERSON><PERSON><PERSON>", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "", "Running": "", "Save": "<PERSON><PERSON>", "Save & Create": "Salva e crea", "Save & Update": "Salva e aggiorna", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Il salvataggio dei registri della chat direttamente nell'archivio del browser non è più supportato. Si prega di dedicare un momento per scaricare ed eliminare i registri della chat facendo clic sul pulsante in basso. Non preoccuparti, puoi facilmente reimportare i registri della chat nel backend tramite", "Scroll to bottom when switching between branches": "", "Search": "Cerca", "Search a model": "Cerca un modello", "Search Base": "", "Search Chats": "Cerca nelle chat", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "", "Search Knowledge": "", "Search Models": "Cerca modelli", "Search options": "", "Search Prompts": "Cerca prompt", "Search Result Count": "Conteggio dei risultati della ricerca", "Search Tools": "", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "Searxng Query URL", "See readme.md for instructions": "Vedi readme.md per le istruzioni", "See what's new": "Guarda le novità", "Seed": "<PERSON><PERSON>", "Select a base model": "Selezionare un modello di base", "Select a engine": "", "Select a function": "", "Select a group": "", "Select a model": "Seleziona un modello", "Select a pipeline": "Selezionare una tubazione", "Select a pipeline url": "Selezionare l'URL di una pipeline", "Select a tool": "", "Select Engine": "", "Select Knowledge": "", "Select model": "Seleziona modello", "Select only one model to call": "", "Selected model(s) do not support image inputs": "I modelli selezionati non supportano l'input di immagini", "Semantic distance to query": "", "Send": "Invia", "Send a message": "", "Send a Message": "Invia un messaggio", "Send message": "Invia messaggio", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "Settembre", "Serper API Key": "Chiave API Serper", "Serply API Key": "", "Serpstack API Key": "Chiave API Serpstack", "Server connection verified": "Connessione al server verificata", "Set as default": "Imposta come predefinito", "Set CFG Scale": "", "Set Default Model": "Imposta modello predefinito", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Imposta modello di embedding (ad esempio {{model}})", "Set Image Size": "Imposta dimensione immagine", "Set reranking model (e.g. {{model}})": "Imposta modello di riclassificazione (ad esempio {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "<PERSON><PERSON><PERSON> passaggi", "Set Task Model": "Imposta modello di attività", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "Imposta voce", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Impostazioni", "Settings saved successfully!": "Impostazioni salvate con successo!", "Share": "Condi<PERSON><PERSON>", "Share Chat": "<PERSON><PERSON><PERSON><PERSON> chat", "Share to OpenWebUI Community": "Condividi con la comunità OpenWebUI", "Show": "Mostra", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show shortcuts": "Mostra", "Show your support!": "", "Sign in": "Accedi", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "<PERSON><PERSON><PERSON>", "Sign up": "Registrati", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "Fonte", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "Errore di riconoscimento vocale: {{error}}", "Speech-to-Text Engine": "Motore da voce a testo", "Stop": "", "Stop Sequence": "Sequenza di arresto", "Stream Chat Response": "", "STT Model": "", "STT Settings": "Impostazioni STT", "Success": "Successo", "Successfully updated.": "Aggiornato con successo.", "Suggested prompts to get you started": "", "Support": "", "Support this plugin:": "", "Sync directory": "", "System": "Sistema", "System Instructions": "", "System Prompt": "Prompt di sistema", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "", "Tavily API Key": "", "Temperature": "Temperatura", "Template": "<PERSON><PERSON>", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "Motore da testo a voce", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Grazie per il tuo feedback!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Il punteggio dovrebbe essere un valore compreso tra 0.0 (0%) e 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "<PERSON><PERSON>", "Thinking...": "", "This action cannot be undone. Do you wish to continue?": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON><PERSON><PERSON> garan<PERSON>ce che le tue preziose conversazioni siano salvate in modo sicuro nel tuo database backend. Grazie!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Suggerimento: aggiorna più slot di variabili consecutivamente premendo il tasto tab nell'input della chat dopo ogni sostituzione.", "Title": "<PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "<PERSON><PERSON> (ad esempio Dimmi un fatto divertente)", "Title Auto-Generation": "Generazione automatica del titolo", "Title cannot be an empty string.": "Il titolo non può essere una stringa vuota.", "Title Generation Prompt": "Prompt di generazione del titolo", "TLS": "", "To access the available model names for downloading,": "Per accedere ai nomi dei modelli disponibili per il download,", "To access the GGUF models available for downloading,": "Per accedere ai modelli GGUF disponibili per il download,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "<PERSON><PERSON><PERSON>", "Toggle settings": "Attiva/disattiva impostazioni", "Toggle sidebar": "Attiva/disattiva barra laterale", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool updated successfully": "", "Tools": "", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "Problemi di accesso a Ollama?", "TTS Model": "", "TTS Settings": "Impostazioni TTS", "TTS Voice": "", "Type": "Digitare", "Type Hugging Face Resolve (Download) URL": "Digita l'URL di Hugging Face Resolve (Download)", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "", "Update and Copy Link": "Aggiorna e copia link", "Update for the latest features and improvements.": "", "Update password": "Aggiorna password", "Updated": "", "Updated at": "", "Updated At": "", "Upload": "", "Upload a GGUF model": "Carica un modello GGUF", "Upload directory": "", "Upload files": "", "Upload Files": "Carica file", "Upload Pipeline": "", "Upload Progress": "Avanzamento caricamento", "URL": "", "URL Mode": "Modalità URL", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "<PERSON><PERSON> Gravatar", "Use groups to group your users and assign permissions.": "", "Use Initials": "<PERSON>a iniz<PERSON>i", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "utente", "User": "", "User location successfully retrieved.": "", "Username": "", "Users": "<PERSON><PERSON><PERSON>", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "Utilizza", "Valid time units:": "Unità di tempo valide:", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "variabile", "variable to have them replaced with clipboard content.": "variabile per farli sostituire con il contenuto degli appunti.", "Version": "Versione", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "", "Voice Input": "", "Warning": "Avvertimento", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Attenzione: se aggiorni o cambi il tuo modello di embedding, dovrai reimportare tutti i documenti.", "Web": "Web", "Web API": "", "Web Loader Settings": "Impostazioni del caricatore Web", "Web Search": "Ricerca sul Web", "Web Search Engine": "Motore di ricerca Web", "Web Search Query Generation": "", "Webhook URL": "URL webhook", "WebUI Settings": "Impostazioni WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "Novità in", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "", "Widescreen Mode": "", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "Area di lavoro", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "Scrivi un suggerimento per il prompt (ad esempio Chi sei?)", "Write a summary in 50 words that summarizes [topic or keyword].": "<PERSON><PERSON><PERSON> un riassunto in 50 parole che riassume [argomento o parola chiave].", "Write something...": "", "Write your model template content here": "", "Yesterday": "<PERSON><PERSON>", "You": "Tu", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "", "You have no archived conversations.": "Non hai conversazioni archiviate.", "You have shared this chat": "Hai condiviso questa chat", "You're a helpful assistant.": "Sei un assistente utile.", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "Youtube", "Youtube Loader Settings": "Impostazioni del caricatore Youtube"}